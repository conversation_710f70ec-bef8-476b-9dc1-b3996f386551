/* Expiry Tracker Styles */
:root {
    --primary-color: #6366f1;
    --secondary-color: #1e293b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --light-gray: #f8fafc;
    --border-color: #e2e8f0;
    --text-color: #334155;
    --text-muted: #64748b;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-gray);
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header h1 {
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 600;
}

.header h1 i {
    margin-right: 10px;
    color: var(--warning-color);
}

.header-actions {
    display: flex;
    gap: 15px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #5855eb;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #0f172a;
    transform: translateY(-2px);
}

.btn-info {
    background-color: var(--info-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Alert Summary */
.alert-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.alert-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
}

.alert-card:hover {
    transform: translateY(-5px);
}

.alert-card.active {
    border-left: 4px solid var(--warning-color);
}

.alert-card.expired {
    border-left: 4px solid var(--danger-color);
}

.alert-card.disposed {
    border-left: 4px solid var(--secondary-color);
}

.alert-card.returned {
    border-left: 4px solid var(--info-color);
}

.alert-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.alert-card.active .alert-icon {
    background-color: var(--warning-color);
}

.alert-card.expired .alert-icon {
    background-color: var(--danger-color);
}

.alert-card.disposed .alert-icon {
    background-color: var(--secondary-color);
}

.alert-card.returned .alert-icon {
    background-color: var(--info-color);
}

.alert-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.alert-content p {
    color: var(--text-muted);
    font-size: 14px;
}

/* Filters */
.filters {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.filter-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-color);
    font-size: 14px;
}

.filter-group select,
.filter-group input {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Table */
.table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background-color: var(--light-gray);
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
}

.data-table td {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.data-table tbody tr:hover {
    background-color: var(--light-gray);
}

.data-table tbody tr.critical {
    background-color: #fef3c7;
    border-left: 4px solid var(--warning-color);
}

.data-table tbody tr.expired {
    background-color: #fee2e2;
    border-left: 4px solid var(--danger-color);
}

/* Product Info */
.product-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.product-image {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    object-fit: cover;
}

.product-name {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.product-sku {
    font-size: 12px;
    color: var(--text-muted);
}

/* Status Badges */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: #dcfce7;
    color: #166534;
}

.status-expired {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-disposed {
    background-color: #e2e8f0;
    color: #475569;
}

.status-returned {
    background-color: #dbeafe;
    color: #1e40af;
}

/* Expiry Categories */
.expiry-category {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    color: white;
    text-align: center;
    min-width: 80px;
}

.expiry-category.default {
    background-color: var(--secondary-color);
}

/* Expiry Dates and Days */
.expiry-date,
.days-left {
    font-weight: 500;
}

.expiry-date.critical,
.days-left.critical {
    color: var(--warning-color);
}

.expiry-date.expired,
.days-left.expired {
    color: var(--danger-color);
}

.days-left .expired {
    color: var(--danger-color);
    font-weight: 600;
}

/* Quantity */
.quantity {
    font-weight: 600;
    color: var(--text-color);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.action-buttons .btn {
    padding: 6px 10px;
    font-size: 12px;
}

/* Pagination */
.pagination {
    text-align: center;
    color: var(--text-muted);
    font-size: 14px;
}

/* No Data */
.no-data {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 40px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .header-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .alert-summary {
        grid-template-columns: 1fr;
    }
    
    .filter-form {
        grid-template-columns: 1fr;
    }
    
    .data-table {
        font-size: 12px;
    }
    
    .data-table th,
    .data-table td {
        padding: 10px 8px;
    }
    
    .product-info {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 5px;
    }
    
    .action-buttons .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.5rem;
    }
    
    .alert-card {
        padding: 15px;
    }
    
    .alert-content h3 {
        font-size: 1.5rem;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 13px;
    }
}

/* Form Styles */
.form-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.expiry-form {
    max-width: 800px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 500;
    color: var(--text-color);
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-help {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: var(--text-muted);
    font-style: italic;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group .error-message {
    color: var(--danger-color);
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group small {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 4px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-start;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

/* Item Details */
.item-details {
    margin-bottom: 30px;
}

.detail-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.detail-header {
    background: var(--primary-color);
    color: white;
    padding: 20px;
}

.detail-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.detail-content {
    padding: 20px;
}

.detail-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-group label {
    font-weight: 600;
    color: var(--text-muted);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-group span {
    font-size: 16px;
    color: var(--text-color);
    font-weight: 500;
}

/* Action Form */
.action-form-container {
    margin-bottom: 30px;
}

.action-form {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.form-header {
    background: var(--warning-color);
    color: white;
    padding: 20px;
}

.form-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.handle-expiry-form {
    padding: 30px;
}

/* Alerts */
.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid;
}

.alert-success {
    background-color: #dcfce7;
    color: #166534;
    border-left-color: var(--success-color);
}

.alert-error {
    background-color: #fee2e2;
    color: #991b1b;
    border-left-color: var(--danger-color);
}

.alert-info {
    background-color: #dbeafe;
    color: #1e40af;
    border-left-color: var(--info-color);
}

/* Quantity Remaining */
.quantity-remaining {
    font-weight: 700;
    color: var(--primary-color);
}

/* Print Styles */
@media print {
    .header-actions,
    .filters,
    .action-buttons,
    .form-actions {
        display: none;
    }
    
    .data-table {
        font-size: 10px;
    }
    
    .data-table th,
    .data-table td {
        padding: 5px;
    }
    
    .form-container,
    .action-form-container {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* Enhanced Form Styles */
.quantity-info,
.cost-info,
.revenue-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 8px 12px;
    background-color: var(--light-gray);
    border-radius: 6px;
    font-size: 12px;
}

.quantity-info .remaining-quantity,
.cost-info .unit-cost,
.revenue-info .potential-revenue {
    color: var(--text-muted);
}

.quantity-info .unit-info,
.cost-info .total-cost {
    color: var(--primary-color);
    font-weight: 600;
}

/* Financial Summary */
.financial-summary {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    margin: 20px 0;
    overflow: hidden;
}

.summary-header {
    background: var(--primary-color);
    color: white;
    padding: 15px 20px;
}

.summary-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.summary-header i {
    margin-right: 8px;
}

.summary-content {
    padding: 20px;
}

.summary-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 15px;
}

.summary-row:last-child {
    margin-bottom: 0;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.summary-item label {
    font-weight: 600;
    color: var(--text-muted);
    font-size: 14px;
}

.summary-item span {
    font-weight: 700;
    font-size: 16px;
}

.net-impact.positive {
    color: var(--success-color);
}

.net-impact.negative {
    color: var(--danger-color);
}

.net-impact.neutral {
    color: var(--text-muted);
}

/* Enhanced Form Help */
.form-help i {
    margin-right: 5px;
    color: var(--info-color);
}

.form-help .cost-tip,
.form-help .revenue-tip {
    display: block;
    margin-top: 3px;
    font-weight: 500;
    color: var(--primary-color);
}

/* Responsive adjustments for new elements */
@media (max-width: 768px) {
    .summary-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .quantity-info,
    .cost-info,
    .revenue-info {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}
