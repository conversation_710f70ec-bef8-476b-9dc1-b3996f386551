/* Inventory-specific styles */
.inventory-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark, #4f46e5) 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.inventory-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.inventory-stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.inventory-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.inventory-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.inventory-stat-value {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.inventory-stat-label {
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.inventory-stat-change {
    font-size: 0.75rem;
    font-weight: 600;
}

.inventory-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.inventory-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    text-decoration: none;
    color: #1e293b;
    transition: all 0.3s ease;
    font-weight: 600;
    text-align: center;
}

.inventory-action-btn:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.inventory-action-btn i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.inventory-action-btn .btn-text {
    font-size: 0.875rem;
}

/* Order forms */
.order-form {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.order-form-header {
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.order-form-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.order-form-subtitle {
    color: #64748b;
    font-size: 0.875rem;
}

.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #f8fafc;
}

.form-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.form-section-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

/* Product selection */
.product-selection {
    max-height: 400px;
    overflow-y: auto;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background: white;
    transition: all 0.2s ease;
}

.product-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
}

.product-info {
    flex: 1;
    margin-left: 1rem;
}

.product-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.product-details {
    color: #64748b;
    font-size: 0.875rem;
}

.product-price {
    font-weight: 600;
    color: var(--primary-color);
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.quantity-input {
    width: 60px;
    text-align: center;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 0.25rem;
}

/* Order summary */
.order-summary {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.summary-row.total {
    border-top: 1px solid #e2e8f0;
    padding-top: 1rem;
    font-weight: 600;
    font-size: 1.1rem;
    color: #1e293b;
}

/* Status badges */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background: #fef3c7;
    color: #d97706;
}

.status-sent {
    background: #dbeafe;
    color: #2563eb;
}

.status-waiting_for_delivery {
    background: #fff3cd;
    color: #856404;
}

.status-received {
    background: #d1fae5;
    color: #059669;
}

.status-cancelled {
    background: #fee2e2;
    color: #dc2626;
}

/* Alert styles */
.inventory-alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-low-stock {
    background: #fef3c7;
    border-left-color: #d97706;
    color: #92400e;
}

.alert-out-of-stock {
    background: #fee2e2;
    border-left-color: #dc2626;
    color: #991b1b;
}

.alert-success {
    background: #d1fae5;
    border-left-color: #059669;
    color: #065f46;
}

/* Multi-step progress indicator */
.multi-step-progress {
    margin-bottom: 2rem;
}

.progress-steps {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
}

.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.step.active .step-circle {
    background: var(--primary-color, #6366f1);
    color: white;
}

.step.completed .step-circle {
    background: #28a745;
    color: white;
}

.step-label {
    font-size: 0.875rem;
    margin-top: 0.5rem;
    color: #6c757d;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
}

.step.active .step-label {
    color: var(--primary-color, #6366f1);
    font-weight: 600;
}

.step.completed .step-label {
    color: #28a745;
}

.step-line {
    flex: 1;
    height: 2px;
    background: #e9ecef;
    margin: 0 1rem;
    position: relative;
    top: -20px;
}

.step.completed + .step-line {
    background: #28a745;
}

.step.active ~ .step-line {
    background: var(--primary-color, #6366f1);
}

/* Search functionality styles */
.search-results {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #f8fafc;
    padding: 1rem;
}

.search-suggestions {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background: white;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    max-height: 300px;
    overflow-y: auto;
}

.suggestion-item {
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
    border-bottom: 1px solid #f8f9fa;
}

.suggestion-item:hover,
.suggestion-item.active {
    background-color: #f8f9fa !important;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item .badge {
    font-size: 0.7rem;
}

.cursor-pointer {
    cursor: pointer;
}

/* Alert message styles */
.alert {
    border-radius: 0.375rem;
    border: none;
    font-size: 0.875rem;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.search-result-item {
    background: white;
    transition: all 0.2s ease;
}

.search-result-item:hover {
    background: #f8fafc;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-result-item .add-to-order-btn {
    white-space: nowrap;
}

/* Enhanced product display */
.product-item {
    transition: all 0.2s ease;
}

.product-item:hover {
    background: #f8fafc;
}

.product-item .badge {
    font-size: 0.75rem;
}

/* Search form enhancements */
.form-text {
    margin-top: 0.25rem;
}

.form-text .text-muted {
    font-size: 0.8rem;
}

/* Modal search enhancements */
.modal .product-list {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    background: #f8fafc;
}

/* Responsive design */
@media (max-width: 768px) {
    .inventory-header {
        padding: 1.5rem;
    }

    .inventory-stats {
        grid-template-columns: 1fr;
    }

    .inventory-actions {
        grid-template-columns: repeat(2, 1fr);
    }

    .order-form {
        padding: 1.5rem;
    }

    .product-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .product-info {
        margin-left: 0;
        margin-top: 0.5rem;
        width: 100%;
    }

    /* Mobile multi-step adjustments */
    .progress-steps {
        flex-direction: column;
        gap: 1rem;
    }

    .step-line {
        width: 2px;
        height: 20px;
        margin: 0;
        position: static;
        order: -1;
    }

    .step {
        flex-direction: row;
        min-width: auto;
    }

    .step-circle {
        width: 30px;
        height: 30px;
        margin-right: 0.5rem;
    }

    .step-label {
        margin-top: 0;
        margin-left: 0.5rem;
        text-align: left;
        min-width: auto;
        font-size: 0.8rem;
    }

    /* Mobile search adjustments */
    .search-result-item {
        padding: 1rem !important;
    }

    .search-result-item .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .search-result-item .add-to-order-btn {
        width: 100%;
        margin-top: 0.5rem;
    }

    /* Mobile multi-step navigation */
    .multi-step-navigation .d-flex {
        flex-direction: column;
        gap: 0.5rem;
    }

    .multi-step-navigation .btn {
        width: 100%;
    }
}
