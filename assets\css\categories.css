/* Categories Management Styles */
:root {
    --primary-color: #6366f1;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-color: #e2e8f0;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --border-radius: 8px;
    --transition: all 0.2s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f1f5f9;
    color: var(--dark-color);
    line-height: 1.6;
}

/* Layout */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 260px;
    height: 100vh;
    background: var(--sidebar-color, #1e293b);
    color: white;
    overflow-y: auto;
    z-index: 1000;
    transition: var(--transition);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h4 {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.sidebar-header small {
    opacity: 0.8;
    font-size: 0.875rem;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-right: 3px solid var(--primary-color, transparent);
}

.nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.15);
    border-right: 3px solid var(--primary-color, white);
}

.nav-link i {
    width: 20px;
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.main-content {
    margin-left: 260px;
    min-height: 100vh;
    background-color: #f1f5f9;
}

/* Header */
.header {
    background: white;
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
}

.header-title h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.header-subtitle {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Content */
.content {
    padding: 2rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-icon.stat-primary {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

.stat-icon.stat-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.stat-icon.stat-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.stat-icon.stat-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--secondary-color);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Category Header */
.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.category-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
}

.category-actions {
    display: flex;
    gap: 1rem;
}

/* Filter Section */
.filter-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow);
}

.filter-row {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1rem;
    align-items: end;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.invalid-feedback {
    display: block;
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.form-text {
    color: var(--secondary-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-color);
    opacity: 0.9;
    color: white;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
    color: white;
}

.btn-outline-secondary {
    background: transparent;
    color: var(--secondary-color);
    border: 1px solid var(--border-color);
}

.btn-outline-secondary:hover {
    background: var(--secondary-color);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
}

/* Data Section */
.data-section {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 0;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
}

/* Table */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.table th,
.table td {
    padding: 0.75rem 1.5rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--dark-color);
}

.table tr:hover {
    background: rgba(99, 102, 241, 0.02);
}

/* Sort Links */
.sort-link {
    color: var(--dark-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sort-link:hover {
    color: var(--primary-color);
}

/* Category Icon */
.category-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-description {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Product Count */
.product-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-primary {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

.badge-secondary {
    background: rgba(100, 116, 139, 0.1);
    color: var(--secondary-color);
}

.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.badge-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* Category Form */
.category-form {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
}

/* Pagination */
.pagination-wrapper {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
}

.pagination-info {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.pagination {
    display: flex;
    list-style: none;
    gap: 0.25rem;
    margin: 0;
    padding: 0;
}

.page-item .page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: var(--secondary-color);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.page-item .page-link:hover,
.page-item.active .page-link {
    background: var(--primary-color);
    color: white;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* Utilities */
.d-flex {
    display: flex;
}

.align-items-center {
    align-items: center;
}

.justify-content-between {
    justify-content: space-between;
}

.gap-1 {
    gap: 0.25rem;
}

.gap-2 {
    gap: 0.5rem;
}

.gap-3 {
    gap: 1rem;
}

.text-center {
    text-align: center;
}

.text-muted {
    color: var(--secondary-color);
}

.text-success {
    color: var(--success-color);
}

.text-warning {
    color: var(--warning-color);
}

.text-danger {
    color: var(--danger-color);
}

.text-info {
    color: var(--info-color);
}

.text-primary {
    color: var(--primary-color);
}

.text-secondary {
    color: var(--secondary-color);
}

.font-weight-bold {
    font-weight: 600;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 1rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

.me-2 {
    margin-right: 0.5rem;
}

.me-3 {
    margin-right: 1rem;
}

.ms-1 {
    margin-left: 0.25rem;
}

.ms-2 {
    margin-left: 0.5rem;
}

.py-4 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}

.ml-auto {
    margin-left: auto;
}

/* Currency */
.currency::before {
    font-weight: normal;
}

/* Row and Column Grid */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.col-md-3,
.col-md-4,
.col-md-6 {
    padding: 0 0.75rem;
    margin-bottom: 1rem;
}

.col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

/* List Styles */
.list-unstyled {
    list-style: none;
    padding-left: 0;
}

.list-unstyled li {
    padding: 0.25rem 0;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .header-content {
        padding: 1rem;
    }
    
    .content {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
    }
    
    .category-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .col-md-3,
    .col-md-4,
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .pagination-wrapper {
        flex-direction: column;
        gap: 1rem;
    }
    
    .table-responsive {
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.75rem;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .header-actions,
    .category-actions,
    .filter-section,
    .pagination-wrapper {
        display: none;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .content {
        padding: 0;
    }
}