:root {
    --primary-color: #6366f1;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --light-gray: #f8fafc;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.2s ease-in-out;
}

/* Main Layout */
.main-content {
    margin-left: 250px;
    min-height: 100vh;
    background-color: #f8fafc;
}

.header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 1.5rem 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title h1 {
    margin: 0;
    color: var(--dark-color);
    font-size: 1.875rem;
    font-weight: 700;
}

.header-subtitle {
    color: var(--secondary-color);
    margin-top: 0.25rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    background: var(--light-color);
    border-radius: 0.5rem;
}

.user-avatar {
    width: 2rem;
    height: 2rem;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Content Area */
.content {
    padding: 2rem;
}

/* Filter Section */
.filter-section {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-row {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #5855eb;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-outline-secondary {
    background: transparent;
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
}

.btn-outline-secondary:hover {
    background: var(--secondary-color);
    color: white;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-outline-danger {
    background: transparent;
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.btn-outline-danger:hover {
    background: var(--danger-color);
    color: white;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

/* Table Styles */
.product-table {
    background: white;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
}

.table th {
    background: #f9fafb;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--dark-color);
    border-bottom: 1px solid #e5e7eb;
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: middle;
}

.table tbody tr:hover {
    background: #f9fafb;
}

/* Product Image Placeholder */
.product-image-placeholder {
    width: 3rem;
    height: 3rem;
    background: var(--light-color);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--secondary-color);
    font-size: 1.25rem;
}

/* Badges */
.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-primary {
    background: #dbeafe;
    color: #1e40af;
}

.badge-success {
    background: #d1fae5;
    color: #065f46;
}

.badge-secondary {
    background: #e5e7eb;
    color: #374151;
}

.badge-warning {
    background: #fef3c7;
    color: #92400e;
}

.badge-danger {
    background: #fee2e2;
    color: #991b1b;
}

/* Button Groups */
.btn-group {
    display: flex;
    gap: 0.25rem;
}

/* Bulk Actions */
.bulk-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.bulk-actions select,
.bulk-actions textarea {
    min-width: 150px;
}

/* Pagination */
.pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
}

.pagination-info {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.pagination {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.25rem;
}

.page-item .page-link {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    color: var(--primary-color);
    text-decoration: none;
    border-radius: 0.375rem;
    transition: all 0.2s;
}

.page-item .page-link:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-item.active .page-link {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid transparent;
}

.alert-success {
    background: #d1fae5;
    color: #065f46;
    border-color: #a7f3d0;
}

.alert-danger {
    background: #fee2e2;
    color: #991b1b;
    border-color: #fca5a5;
}

.alert-dismissible .btn-close {
    float: right;
    background: transparent;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0;
    margin-left: 1rem;
}

/* Modal Styles */
.modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    border-bottom: 1px solid #e5e7eb;
    padding: 1.5rem;
    flex-shrink: 0;
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: calc(90vh - 200px);
}

.modal-footer {
    border-top: 1px solid #e5e7eb;
    padding: 1.5rem;
    flex-shrink: 0;
    background: white;
    position: sticky;
    bottom: 0;
    z-index: 10;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.modal-footer .btn {
    min-width: 120px;
    font-weight: 600;
}

.deactivation-option {
    transition: all 0.2s ease;
}

.deactivation-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.modal-body .btn {
    text-align: left;
    padding: 1rem;
    border-radius: 8px;
}

.modal-body .btn strong {
    color: #333;
}

.modal-body .btn small {
    font-size: 0.85rem;
}

/* Tab Navigation Styles */
.nav-tabs {
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--secondary-color);
    font-weight: 500;
    padding: 0.75rem 1rem;
    margin-right: 0.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    background-color: #f3f4f6;
    color: var(--dark-color);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: white;
    border-bottom-color: var(--primary-color);
    font-weight: 600;
}

.nav-tabs .nav-link i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

#deactivationType {
    min-width: 200px;
}

/* Enhanced Bulk Actions */
.bulk-actions-container {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.bulk-header {
    background: linear-gradient(90deg, var(--light-gray) 0%, #f1f5f9 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bulk-selection-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.selection-count {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: white;
    flex-wrap: wrap;
}

.bulk-actions.hidden {
    display: none;
}

.bulk-action-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: white;
    transition: var(--transition);
}

.bulk-action-group:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.bulk-progress {
    background: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 1rem 0;
    border: 1px solid var(--border-color);
    display: none;
}

.progress-bar-container {
    background: var(--light-gray);
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color) 0%, #4f46e5 100%);
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

/* Enhanced Supplier Row Selection */
.supplier-row.selected {
    background-color: rgba(99, 102, 241, 0.05);
    border-left: 4px solid var(--primary-color);
    transform: translateX(2px);
}

.supplier-checkbox:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Advanced Filters */
.advanced-filters {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    display: none;
    transition: var(--transition);
}

.advanced-filters.show {
    display: block;
    animation: slideDown 0.3s ease-in-out;
}

.filter-toggle {
    background: none;
    border: none;
    color: var(--primary-color);
    font-weight: 600;
    padding: 0;
    text-decoration: underline;
    cursor: pointer;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.filter-toggle:hover {
    color: #4f46e5;
}

.filter-tags {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.filter-tag {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: fadeIn 0.2s ease-in-out;
}

.filter-tag .remove-tag {
    background: none;
    border: none;
    color: white;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.filter-tag .remove-tag:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Enhanced Performance Metrics */
.performance-tooltip {
    position: relative;
    cursor: help;
    border-bottom: 1px dotted var(--text-secondary);
}

.performance-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.85rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 0.5rem;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    box-shadow: var(--box-shadow);
}

.performance-tooltip::before {
    content: '';
    position: absolute;
    bottom: calc(100% - 0.25rem);
    left: 50%;
    transform: translateX(-50%);
    border: 0.25rem solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
}

.performance-tooltip:hover::after,
.performance-tooltip:hover::before {
    opacity: 1;
}

/* Enhanced Performance Color Coding */
.text-excellent { color: #059669 !important; font-weight: 600; }
.text-good { color: #0ea5e9 !important; font-weight: 600; }
.text-fair { color: #f59e0b !important; font-weight: 600; }
.text-poor { color: #f97316 !important; font-weight: 600; }
.text-critical { color: #dc2626 !important; font-weight: 600; }

.bg-excellent { background-color: #dcfce7 !important; color: #059669; }
.bg-good { background-color: #dbeafe !important; color: #0ea5e9; }
.bg-fair { background-color: #fef3c7 !important; color: #f59e0b; }
.bg-poor { background-color: #fed7aa !important; color: #f97316; }
.bg-critical { background-color: #fecaca !important; color: #dc2626; }

/* Quick Actions Panel */
.quick-actions {
    position: fixed;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    background: white;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-right: none;
    z-index: 900;
    transition: var(--transition);
    opacity: 0.8;
}

.quick-actions:hover {
    opacity: 1;
    transform: translateY(-50%) translateX(-5px);
}

.quick-action-btn {
    display: block;
    width: 100%;
    margin-bottom: 0.5rem;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
    color: var(--text-primary);
    text-align: center;
    font-weight: 600;
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateX(-2px);
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.stat-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stat-card-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-right: 1rem;
}

.stat-card-title {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.stat-card-trend {
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.trend-up { color: var(--success-color); }
.trend-down { color: var(--danger-color); }
.trend-neutral { color: var(--text-secondary); }

/* Floating Action Button */
.floating-actions {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.fab {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, #4f46e5 100%);
    color: white;
    border: none;
    box-shadow: var(--box-shadow-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: var(--transition);
    cursor: pointer;
    margin-bottom: 1rem;
}

.fab:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.fab-mini {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
}

/* Enhanced Form Controls */
.form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    font-size: 0.95rem;
    transition: var(--transition);
    background: white;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.input-group {
    position: relative;
}

.input-group-text {
    background: var(--light-gray);
    border: 2px solid var(--border-color);
    border-right: none;
    color: var(--text-secondary);
    font-weight: 600;
}

.input-group .form-control {
    border-left: none;
}

.input-group .form-control:focus {
    border-left: none;
}

/* Enhanced Search */
.search-container {
    position: relative;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    pointer-events: none;
}

.form-control.with-icon {
    padding-left: 3rem;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-in-out;
}

.slide-down {
    animation: slideDown 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.btn:focus-visible,
.form-control:focus-visible,
.form-check-input:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .table th:nth-child(n+8),
    .table td:nth-child(n+8) {
        display: none;
    }
}

@media (max-width: 992px) {
    .filter-row {
        flex-direction: column;
    }
    
    .form-group {
        min-width: 100%;
    }
    
    .bulk-actions {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .quick-actions {
        display: none;
    }
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .filter-row {
        flex-direction: column;
    }
    
    .form-group {
        min-width: auto;
    }
    
    .table {
        font-size: 0.875rem;
    }
    
    .table th,
    .table td {
        padding: 0.75rem 0.5rem;
    }
    
    .table th:nth-child(n+6),
    .table td:nth-child(n+6) {
        display: none;
    }
    
    .btn-group {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .bulk-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-card-value {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .table th:nth-child(n+4),
    .table td:nth-child(n+4) {
        display: none;
    }
    
    .filter-section {
        padding: 1rem;
    }
    
    .bulk-actions {
        padding: 1rem;
    }
}
