/* Shelf Label Styles */

.shelf-label-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.label-preview {
    background: white;
    border: 2px solid #000;
    border-radius: 8px;
    padding: 15px;
    margin: 10px;
    text-align: center;
    font-family: <PERSON><PERSON>, sans-serif;
}

.label-preview.small {
    width: 200px;
    height: 100px;
    font-size: 10px;
}

.label-preview.standard {
    width: 300px;
    height: 200px;
    font-size: 12px;
}

.label-preview.large {
    width: 400px;
    height: 300px;
    font-size: 14px;
}

.label-header {
    border-bottom: 1px solid #000;
    padding-bottom: 5px;
    margin-bottom: 10px;
    font-weight: bold;
}

.label-product-name {
    font-weight: bold;
    margin: 10px 0;
    word-wrap: break-word;
}

.label-details {
    font-size: 0.9em;
    margin: 5px 0;
}

.label-price {
    font-size: 1.2em;
    font-weight: bold;
    color: #2563eb;
    margin: 10px 0;
}

.label-footer {
    border-top: 1px solid #000;
    padding-top: 5px;
    margin-top: 10px;
    font-size: 0.8em;
    color: #666;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .label-preview {
        border: 1px solid #000 !important;
        margin: 2px !important;
        page-break-inside: avoid !important;
    }
    
    body {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
}

/* Bulk Operations */
.bulk-actions {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.bulk-actions .form-check {
    margin-bottom: 1rem;
}

.bulk-actions .btn-group {
    gap: 0.5rem;
}

/* Product Grid */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

        .product-card {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-card.selected {
    border-color: var(--primary-color);
    background: #f8fafc;
}

        .product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 0.75rem;
        }

        .product-info h5 {
            margin-bottom: 0.25rem;
            color: #1e293b;
            font-size: 1rem;
        }

        .product-meta {
            color: #64748b;
            font-size: 0.8rem;
            margin-bottom: 0.75rem;
        }

        .product-price {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }



/* Filters Section */
.filters-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.select-all-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.select-all-container .form-check {
    margin-bottom: 0;
}

.select-all-container .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .bulk-actions .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .bulk-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .select-all-container {
        flex-direction: column;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .product-grid {
        grid-template-columns: 1fr;
    }
    
    .product-card {
        padding: 1rem;
    }
    
    .bulk-actions {
        padding: 1rem;
    }
}
