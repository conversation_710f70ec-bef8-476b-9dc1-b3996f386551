/**
 * Enhanced Payment Processing System for POS
 * Handles multiple payment methods, validation, and receipt generation
 */

class PaymentProcessor {
    constructor() {
        this.selectedPaymentMethods = new Set();
        this.paymentAmount = 0;
        this.transactionData = null;
        this.currency = '$';
        this.taxRate = 0;
        this.cashSelectedCustomer = null;
        this.loyaltySelectedCustomer = null;
        this.paymentBreakdown = new Map(); // method -> amount
        this.totalPaid = 0;
        this.remainingBalance = 0;
        this.loyaltySettings = null;
        
        this.init();
    }

    init() {
        this.currency = window.POSConfig?.currencySymbol || '$';
        this.taxRate = window.POSConfig?.taxRate || 16;
        this.paymentAmount = window.paymentTotals?.total || 0;
        this.transactionData = window.cartData || [];
        
        this.bindEvents();
        this.setupPaymentMethods();
        this.updateCartSummary();
        this.loadLoyaltySettings();
        this.setDefaultPaymentMethod();
    }

    // Method to refresh cart data from current session
    refreshCartData() {
        // Get current cart data from the page
        this.transactionData = window.cartData || [];
        
        // Recalculate totals
        let subtotal = 0;
        if (this.transactionData && this.transactionData.length > 0) {
            this.transactionData.forEach(item => {
                subtotal += parseFloat(item.price) * parseInt(item.quantity);
            });
        }
        const tax = subtotal * (this.taxRate / 100);
        const total = subtotal + tax;
        
        this.paymentAmount = total;
        
        // Update payment summary
        this.updateCartSummary();
        
        // Update confirm button state
        this.updateConfirmButton();
    }

    bindEvents() {
        // Payment method selection
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', (e) => {
                this.togglePaymentMethod(e.currentTarget);
            });
        });

        // Payment method checkbox events
        document.querySelectorAll('.payment-method-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.handlePaymentMethodCheckbox(e);
            });
        });

        // Listen for payment modal show event
        const paymentModal = document.getElementById('paymentModal');
        if (paymentModal) {
            paymentModal.addEventListener('show.bs.modal', () => {
                this.refreshCartData();
                this.bindQuickAmountButtons();
            });
        }

        // Loyalty customer search functionality
        const loyaltyCustomerSearch = document.getElementById('loyaltyCustomerSearch');
        const loyaltyCustomerSearchBtn = document.getElementById('loyaltyCustomerSearchBtn');
        const loyaltyClearCustomer = document.getElementById('loyaltyClearCustomer');

        if (loyaltyCustomerSearch) {
            let searchTimeout;
            loyaltyCustomerSearch.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                const searchTerm = e.target.value.trim();
                if (searchTerm.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        this.searchLoyaltyCustomers(searchTerm);
                    }, 300);
                } else {
                    document.getElementById('loyaltyCustomerResults').style.display = 'none';
                }
            });
        }

        if (loyaltyCustomerSearchBtn) {
            loyaltyCustomerSearchBtn.addEventListener('click', () => {
                const searchTerm = loyaltyCustomerSearch.value.trim();
                if (searchTerm.length >= 2) {
                    this.searchLoyaltyCustomers(searchTerm);
                }
            });
        }

        if (loyaltyClearCustomer) {
            loyaltyClearCustomer.addEventListener('click', () => {
                this.showLoyaltyCustomerSearch();
                loyaltyCustomerSearch.value = '';
            });
        }

        // Cash customer search functionality
        const cashCustomerSearch = document.getElementById('cashCustomerSearch');
        const cashCustomerSearchBtn = document.getElementById('cashCustomerSearchBtn');
        const cashClearCustomer = document.getElementById('cashClearCustomer');

        if (cashCustomerSearch) {
            let searchTimeout;
            cashCustomerSearch.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                const searchTerm = e.target.value.trim();
                if (searchTerm.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        this.searchCashCustomers(searchTerm);
                    }, 300);
                } else {
                    document.getElementById('cashCustomerResults').style.display = 'none';
                }
            });
        }

        if (cashCustomerSearchBtn) {
            cashCustomerSearchBtn.addEventListener('click', () => {
                const searchTerm = cashCustomerSearch.value.trim();
                if (searchTerm.length >= 2) {
                    this.searchCashCustomers(searchTerm);
                }
            });
        }

        if (cashClearCustomer) {
            cashClearCustomer.addEventListener('click', () => {
                this.showCashCustomerSearch();
                cashCustomerSearch.value = '';
                this.cashSelectedCustomer = null;
                this.updateCashLoyaltyPoints();
            });
        }

        // Collapsible button for other payment methods
        const collapseBtn = document.querySelector('[data-bs-target="#otherPaymentMethods"]');
        if (collapseBtn) {
            collapseBtn.addEventListener('click', (e) => {
                const icon = e.currentTarget.querySelector('i');
                if (e.currentTarget.getAttribute('aria-expanded') === 'true') {
                    icon.className = 'bi bi-chevron-down me-1';
                } else {
                    icon.className = 'bi bi-chevron-up me-1';
                }
            });
        }

        // Cash input events
        const cashInput = document.getElementById('cashReceived');
        if (cashInput) {
            cashInput.addEventListener('input', () => {
                this.calculateChange();
                this.updateCashLoyaltyPoints();
            });
            cashInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.confirmPayment();
                }
            });
        }

        // Loyalty amount input events
        const loyaltyAmountInput = document.getElementById('loyaltyAmountToDeduct');
        if (loyaltyAmountInput) {
            loyaltyAmountInput.addEventListener('input', () => {
                this.calculateLoyaltyPoints();
            });
            loyaltyAmountInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.confirmPayment();
                }
            });
        }

        // Loyalty quick amount buttons
        document.querySelectorAll('.loyalty-quick-amount').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleLoyaltyQuickAmount(e);
            });
        });

        // Loyalty max amount button
        const loyaltyMaxBtn = document.getElementById('loyaltyMaxAmountBtn');
        if (loyaltyMaxBtn) {
            loyaltyMaxBtn.addEventListener('click', () => {
                this.setLoyaltyMaxAmount();
            });
        }

        // Bind quick amount buttons
        this.bindQuickAmountButtons();

        // Card number formatting
        const cardNumber = document.getElementById('cardNumber');
        if (cardNumber) {
            cardNumber.addEventListener('input', (e) => this.formatCardNumber(e));
        }

        // Card expiry formatting
        const cardExpiry = document.getElementById('cardExpiry');
        if (cardExpiry) {
            cardExpiry.addEventListener('input', (e) => this.formatCardExpiry(e));
        }

        // Payment buttons
        const cancelBtn = document.querySelector('.payment-btn.cancel');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.cancelPayment());
        }

        const confirmBtn = document.querySelector('.payment-btn.confirm');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => this.confirmPayment());
        }

        // Receipt buttons
        this.bindReceiptEvents();
    }

    bindReceiptEvents() {
        const printBtn = document.querySelector('.receipt-btn.print');
        if (printBtn) {
            printBtn.addEventListener('click', () => this.printReceipt());
        }


        const newTransactionBtn = document.querySelector('.receipt-btn.new-transaction');
        if (newTransactionBtn) {
            newTransactionBtn.addEventListener('click', () => this.startNewTransaction());
        }

        const cancelReceiptBtn = document.querySelector('.receipt-btn.cancel');
        if (cancelReceiptBtn) {
            cancelReceiptBtn.addEventListener('click', () => this.cancelReceipt());
        }
    }

    setupPaymentMethods() {
        // Add hover effects and selection styling
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('mouseenter', () => {
                if (!method.classList.contains('selected')) {
                    method.style.transform = 'translateY(-2px)';
                    method.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                }
            });

            method.addEventListener('mouseleave', () => {
                if (!method.classList.contains('selected')) {
                    method.style.transform = 'translateY(0)';
                    method.style.boxShadow = '';
                }
            });
        });
    }

    setDefaultPaymentMethod() {
        // Auto-select cash payment method
        const cashMethod = document.querySelector('.payment-method[data-method="cash"]');
        if (cashMethod) {
            this.selectedPaymentMethods.add('cash');
            this.paymentBreakdown.set('cash', this.paymentAmount);
            this.totalPaid = this.paymentAmount;
            this.remainingBalance = 0;
            this.updatePaymentSummary();
            this.updatePaymentBreakdown();
            this.showPaymentSection('cash');
            this.updateConfirmButton();
            
            // Focus on cash input after a short delay
            setTimeout(() => {
                const cashInput = document.getElementById('cashReceived');
                if (cashInput) {
                    cashInput.focus();
                }
            }, 300);
        }
    }

    togglePaymentMethod(methodElement) {
        const method = methodElement.dataset.method;
        const checkbox = methodElement.querySelector('.payment-method-checkbox');
        
        if (checkbox) {
            checkbox.checked = !checkbox.checked;
            this.handlePaymentMethodCheckbox({ target: checkbox });
        }
    }

    handlePaymentMethodCheckbox(event) {
        const checkbox = event.target;
        const method = checkbox.value;
        const methodElement = checkbox.closest('.payment-method');
        
        if (checkbox.checked) {
            this.selectedPaymentMethods.add(method);
            // Set default amount to remaining balance or full amount
            const amount = this.remainingBalance > 0 ? this.remainingBalance : this.paymentAmount;
            this.paymentBreakdown.set(method, amount);
            this.updatePaymentAmounts();
        } else {
            this.selectedPaymentMethods.delete(method);
            this.paymentBreakdown.delete(method);
            this.updatePaymentAmounts();
        }
        
        this.updatePaymentSummary();
        this.updatePaymentBreakdown();
        this.updateConfirmButton();
    }

    updatePaymentAmounts() {
        this.totalPaid = Array.from(this.paymentBreakdown.values()).reduce((sum, amount) => sum + amount, 0);
        this.remainingBalance = this.paymentAmount - this.totalPaid;
    }

    updatePaymentSummary() {
        const paymentTotalDisplay = document.getElementById('paymentTotalDisplay');
        const paymentPaidDisplay = document.getElementById('paymentPaidDisplay');
        const paymentRemainingDisplay = document.getElementById('paymentRemainingDisplay');
        
        if (paymentTotalDisplay) {
            paymentTotalDisplay.textContent = this.formatAmount(this.paymentAmount);
        }
        if (paymentPaidDisplay) {
            paymentPaidDisplay.textContent = this.formatAmount(this.totalPaid);
        }
        if (paymentRemainingDisplay) {
            paymentRemainingDisplay.textContent = this.formatAmount(this.remainingBalance);
        }
        
        // Update remaining balance color
        const remainingElement = document.getElementById('paymentRemainingBalance');
        if (this.remainingBalance <= 0) {
            remainingElement.style.color = '#10b981'; // Green
        } else {
            remainingElement.style.color = '#dc3545'; // Red
        }
    }

    updatePaymentBreakdown() {
        const breakdownContainer = document.getElementById('paymentBreakdown');
        const selectedMethodsDiv = document.getElementById('selectedPaymentMethods');
        
        if (this.selectedPaymentMethods.size === 0) {
            selectedMethodsDiv.style.display = 'none';
            return;
        }
        
        selectedMethodsDiv.style.display = 'block';
        
        let html = '';
        this.selectedPaymentMethods.forEach(method => {
            const amount = this.paymentBreakdown.get(method) || 0;
            const methodName = this.formatPaymentMethod(method);
            
            html += `
                <div class="card mb-2">
                    <div class="card-body py-2">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <strong>${methodName}</strong>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">${this.currency}</span>
                                    <input type="number" 
                                           class="form-control payment-amount-input" 
                                           data-method="${method}"
                                           value="${this.formatAmount(amount)}"
                                           step="0.01" 
                                           min="0" 
                                           max="${this.paymentAmount}">
                                </div>
                            </div>
                            <div class="col-md-2 text-end">
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="window.paymentProcessor.removePaymentMethod('${method}')">
                                    <i class="bi bi-x"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        breakdownContainer.innerHTML = html;
        
        // Bind amount input events
        breakdownContainer.querySelectorAll('.payment-amount-input').forEach(input => {
            input.addEventListener('input', (e) => {
                this.updatePaymentAmount(e.target.dataset.method, parseFloat(e.target.value) || 0);
            });
        });
    }

    updatePaymentAmount(method, amount) {
        this.paymentBreakdown.set(method, amount);
        this.updatePaymentAmounts();
        this.updatePaymentSummary();
        this.updateConfirmButton();
    }

    removePaymentMethod(method) {
        this.selectedPaymentMethods.delete(method);
        this.paymentBreakdown.delete(method);
        
        // Uncheck the checkbox
        const checkbox = document.getElementById(`method_${method}`);
        if (checkbox) {
            checkbox.checked = false;
        }
        
        this.updatePaymentAmounts();
        this.updatePaymentSummary();
        this.updatePaymentBreakdown();
        this.updateConfirmButton();
    }

    updateCartSummary() {
        // Calculate cart totals from current cart data
        let subtotal = 0;
        let itemCount = 0;
        
        if (this.transactionData && this.transactionData.length > 0) {
            this.transactionData.forEach(item => {
                subtotal += parseFloat(item.price) * parseInt(item.quantity);
                itemCount += parseInt(item.quantity);
            });
        }
        
        const tax = subtotal * (this.taxRate / 100);
        const total = subtotal + tax;
        
        // Update payment totals
        this.paymentAmount = total;
        window.paymentTotals = { subtotal, tax, total };
        
        // Update the display
        const subtotalEl = document.getElementById('paymentSubtotal');
        const taxEl = document.getElementById('paymentTax');
        const totalEl = document.getElementById('paymentTotal');
        const itemCountEl = document.getElementById('paymentItemCount');
        const itemsEl = document.getElementById('paymentItems');

        if (subtotalEl) subtotalEl.textContent = this.formatAmount(subtotal);
        if (taxEl) taxEl.textContent = this.formatAmount(tax);
        if (totalEl) totalEl.textContent = this.formatAmount(total);
        if (itemCountEl) itemCountEl.textContent = this.transactionData.length;

        // Update items display
        if (itemsEl && this.transactionData.length > 0) {
            let itemsHtml = '';
            this.transactionData.forEach(item => {
                const itemTotal = item.price * item.quantity;
                itemsHtml += `
                    <div class="d-flex justify-content-between py-1">
                        <div>
                            <small class="fw-bold">${item.name}</small>
                            <br>
                            <small class="text-muted">${item.quantity} × ${this.currency}${this.formatAmount(item.price)}</small>
                        </div>
                        <div class="text-end">
                            <small class="fw-bold">${this.currency}${this.formatAmount(itemTotal)}</small>
                        </div>
                    </div>
                `;
            });
            itemsEl.innerHTML = itemsHtml;
        } else if (itemsEl) {
            itemsEl.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="bi bi-cart-x fs-1"></i>
                    <p class="mt-2 mb-1">No items in cart</p>
                    <small>Add products to get started</small>
                </div>
            `;
        }
    }

    selectPaymentMethod(methodElement) {
        // Remove previous selection
        document.querySelectorAll('.payment-method').forEach(m => {
            m.classList.remove('selected');
            m.style.transform = 'translateY(0)';
            m.style.boxShadow = '';
            m.style.borderColor = '';
        });

        // Select new method
        methodElement.classList.add('selected');
        methodElement.style.transform = 'translateY(-2px)';
        methodElement.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
        methodElement.style.borderColor = '#007bff';

        this.selectedPaymentMethod = methodElement.dataset.method;
        this.showPaymentSection(this.selectedPaymentMethod);
        this.updateConfirmButton();

        // If selecting a method that's in the collapsible section, expand it
        if (this.selectedPaymentMethod !== 'cash' && this.selectedPaymentMethod !== 'loyalty_points') {
            const collapseElement = document.getElementById('otherPaymentMethods');
            const collapseBtn = document.querySelector('[data-bs-target="#otherPaymentMethods"]');
            if (collapseElement && collapseBtn) {
                const bsCollapse = new bootstrap.Collapse(collapseElement, { show: true });
                const icon = collapseBtn.querySelector('i');
                if (icon) {
                    icon.className = 'bi bi-chevron-up me-1';
                }
                collapseBtn.setAttribute('aria-expanded', 'true');
            }
        }
    }

    showPaymentSection(method) {
        // Hide all payment sections
        document.getElementById('cashPaymentSection').style.display = 'none';
        document.getElementById('mobileMoneySection').style.display = 'none';
        document.getElementById('cardPaymentSection').style.display = 'none';
        document.getElementById('loyaltyPointsPaymentSection').style.display = 'none';

        // Show relevant section
        switch (method) {
            case 'cash':
                document.getElementById('cashPaymentSection').style.display = 'block';
                setTimeout(() => {
                    const cashInput = document.getElementById('cashReceived');
                    if (cashInput) {
                        try {
                            cashInput.focus();
                        } catch (error) {
                            console.warn('Error focusing cash input:', error);
                        }
                    }
                }, 100);
                break;
            case 'mobile_money':
                document.getElementById('mobileMoneySection').style.display = 'block';
                break;
            case 'credit_card':
            case 'debit_card':
            case 'pos_card':
                document.getElementById('cardPaymentSection').style.display = 'block';
                break;
            case 'loyalty_points':
                const loyaltySection = document.getElementById('loyaltyPointsPaymentSection');
                if (loyaltySection) {
                    loyaltySection.style.display = 'block';
                }
                this.loadCustomerLoyaltyInfo();
                setTimeout(() => {
                    const loyaltyAmountInput = document.getElementById('loyaltyAmountToDeduct');
                    if (loyaltyAmountInput) {
                        try {
                            loyaltyAmountInput.focus();
                        } catch (error) {
                            console.warn('Error focusing loyalty amount input:', error);
                        }
                    }
                }, 200);
                break;
        }
    }

    calculateChange() {
        const cashInput = document.getElementById('cashReceived');
        const changeDisplay = document.getElementById('changeDisplay');
        const changeAmount = document.getElementById('changeAmount');
        
        if (!cashInput || !changeDisplay || !changeAmount) return;

        const cashReceived = parseFloat(cashInput.value) || 0;
        const change = cashReceived - this.paymentAmount;

        // Update change display
        changeAmount.textContent = `${this.currency}${this.formatAmount(Math.abs(change))}`;

        // Update styling
        changeDisplay.classList.remove('positive', 'negative');
        if (change >= 0) {
            changeDisplay.classList.add('positive');
            changeAmount.style.color = '#10b981';
        } else {
            changeDisplay.classList.add('negative');
            changeAmount.style.color = '#ef4444';
            changeAmount.textContent = `${this.currency}${this.formatAmount(Math.abs(change))} insufficient`;
        }

        this.updateConfirmButton();
    }

    formatCardNumber(e) {
        let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
        let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
        e.target.value = formattedValue;
    }

    formatCardExpiry(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length >= 2) {
            value = value.substring(0, 2) + '/' + value.substring(2, 4);
        }
        e.target.value = value;
    }

    updateConfirmButton() {
        const confirmBtn = document.querySelector('.payment-btn.confirm');
        if (!confirmBtn) return;

        let canConfirm = this.selectedPaymentMethods.size > 0 && this.remainingBalance <= 0.01;

        // Validate each selected payment method
        this.selectedPaymentMethods.forEach(method => {
            const amount = this.paymentBreakdown.get(method) || 0;
            if (amount <= 0) {
                canConfirm = false;
            }
        });

        confirmBtn.disabled = !canConfirm;
    }

    async confirmPayment() {
        if (this.selectedPaymentMethods.size === 0) return;

        const confirmBtn = document.querySelector('.payment-btn.confirm');
        if (confirmBtn.disabled) return;

        // Show loading state
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Processing...';

        try {
            const paymentData = await this.processPayment();
            
            if (paymentData.success) {
                this.showReceipt(paymentData);
            } else {
                this.showError(paymentData.error || 'Payment processing failed');
            }
        } catch (error) {
            console.error('Payment error:', error);
            this.showError('Payment processing failed. Please try again.');
        } finally {
            // Reset button
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>Confirm Payment';
        }
    }

    async processPayment() {
        // Calculate totals from current cart data
        let subtotal = 0;
        if (this.transactionData && this.transactionData.length > 0) {
            this.transactionData.forEach(item => {
                subtotal += parseFloat(item.price) * parseInt(item.quantity);
            });
        }
        const tax = subtotal * (this.taxRate / 100);
        const total = subtotal + tax;

        // Get selected customer data
        let selectedCustomer = window.selectedCustomer || null;
        
        // For cash payments, use the cash selected customer if available
        if (this.selectedPaymentMethods.has('cash') && this.cashSelectedCustomer) {
            selectedCustomer = {
                id: this.cashSelectedCustomer.id,
                display_name: this.cashSelectedCustomer.display_name,
                customer_type: 'registered',
                membership_level: this.cashSelectedCustomer.membership_level
            };
        }
        
        // Build payments array
        const payments = [];
        this.selectedPaymentMethods.forEach(method => {
            const amount = this.paymentBreakdown.get(method) || 0;
            const payment = {
                method: method,
                amount: amount
            };

            // Add method-specific data
            if (method === 'cash') {
                const cashReceived = parseFloat(document.getElementById('cashReceived').value) || 0;
                payment.cash_received = cashReceived;
                payment.change_due = cashReceived - amount;
            } else if (method === 'mobile_money') {
                payment.mobile_number = document.getElementById('mobileNumber').value;
                payment.mobile_provider = document.getElementById('mobileProvider').value;
            } else if (['credit_card', 'debit_card', 'pos_card'].includes(method)) {
                payment.card_number = document.getElementById('cardNumber').value;
                payment.card_expiry = document.getElementById('cardExpiry').value;
                payment.card_cvv = document.getElementById('cardCVV').value;
            } else if (method === 'loyalty_points') {
                const loyaltyValidation = this.validateLoyaltyPayment();
                if (!loyaltyValidation.valid) {
                    throw new Error(loyaltyValidation.error);
                }
                payment.loyalty_points_to_use = loyaltyValidation.pointsToUse;
                payment.loyalty_discount = loyaltyValidation.pointsValue;
                
                // Override customer data with loyalty selected customer
                if (loyaltyValidation.customer) {
                    selectedCustomer = {
                        id: loyaltyValidation.customer.id,
                        display_name: loyaltyValidation.customer.display_name,
                        customer_type: 'registered',
                        membership_level: loyaltyValidation.customer.membership_level || 'Basic'
                    };
                }
            }

            payments.push(payment);
        });
        
        const paymentData = {
            amount: total,
            subtotal: subtotal,
            tax: tax,
            items: this.transactionData,
            payments: payments,
            timestamp: new Date().toISOString(),
            notes: document.getElementById('paymentNotes')?.value || '',
            // Customer information
            customer_id: selectedCustomer ? selectedCustomer.id : null,
            customer_name: selectedCustomer ? selectedCustomer.display_name : 'Walk-in Customer',
            customer_phone: selectedCustomer ? selectedCustomer.phone : '',
            customer_email: selectedCustomer ? selectedCustomer.email : '',
            customer_type: selectedCustomer ? selectedCustomer.customer_type : 'walk_in',
            membership_level: selectedCustomer ? selectedCustomer.membership_level : 'Basic',
            tax_exempt: selectedCustomer ? selectedCustomer.tax_exempt : false
        };

        // Send payment data to server
        const response = await fetch('process_payment.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(paymentData)
        });

        return await response.json();
    }

    showReceipt(paymentData) {
        // Hide payment modal
        const paymentModal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
        if (paymentModal) paymentModal.hide();

        // Populate receipt data
        this.populateReceipt(paymentData);

        // Show/hide auto-print indicator based on setting
        const autoPrintIndicator = document.getElementById('autoPrintIndicator');
        if (autoPrintIndicator) {
            if (window.autoPrintReceipt) {
                autoPrintIndicator.classList.remove('d-none');
            } else {
                autoPrintIndicator.classList.add('d-none');
            }
        }

        // Show receipt modal
        const receiptModal = new bootstrap.Modal(document.getElementById('receiptModal'));
        receiptModal.show();

        // Auto-print receipt if enabled
        if (window.autoPrintReceipt) {
            // Wait a moment for the modal to be fully displayed
            setTimeout(() => {
                this.printReceipt();
            }, 500);
        }
    }

    populateReceipt(paymentData) {
        const now = new Date();
        
        // Set transaction details
        document.querySelector('.receipt-transaction-id').textContent = paymentData.transaction_id || this.generateTransactionId();
        document.querySelector('.receipt-date').textContent = now.toLocaleDateString();
        document.querySelector('.receipt-time').textContent = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', second:'2-digit'});
        
        // Handle multiple payment methods
        if (paymentData.payments && paymentData.payments.length > 1) {
            const paymentMethods = paymentData.payments.map(p => this.formatPaymentMethod(p.method)).join(', ');
            document.querySelector('.receipt-payment-method').textContent = `Multiple (${paymentMethods})`;
        } else if (paymentData.payments && paymentData.payments.length === 1) {
            document.querySelector('.receipt-payment-method').textContent = this.formatPaymentMethod(paymentData.payments[0].method);
        } else {
            document.querySelector('.receipt-payment-method').textContent = this.formatPaymentMethod(paymentData.method);
        }

        // Ensure we have valid numeric values
        const subtotal = parseFloat(paymentData.subtotal) || 0;
        const tax = parseFloat(paymentData.tax) || 0;
        const total = parseFloat(paymentData.amount) || 0;

        // Set totals with proper formatting
        document.querySelector('.receipt-subtotal').textContent = `${this.currency}${this.formatAmount(subtotal)}`;
        document.querySelector('.receipt-tax').textContent = `${this.currency}${this.formatAmount(tax)}`;
        document.querySelector('.receipt-total').textContent = `${this.currency}${this.formatAmount(total)}`;

        // Show cash details if cash payment
        const cashDetails = document.getElementById('receiptCashDetails');
        const cashPayment = paymentData.payments ? paymentData.payments.find(p => p.method === 'cash') : null;
        
        if (cashPayment && cashPayment.cash_received !== undefined) {
            const cashReceived = parseFloat(cashPayment.cash_received) || 0;
            const changeDue = parseFloat(cashPayment.change_due) || 0;
            document.querySelector('.receipt-cash-received').textContent = `${this.currency}${this.formatAmount(cashReceived)}`;
            document.querySelector('.receipt-change-due').textContent = `${this.currency}${this.formatAmount(changeDue)}`;
            cashDetails.style.display = 'block';
        } else {
            cashDetails.style.display = 'none';
        }

        // Populate items
        this.populateReceiptItems(paymentData.items || []);
    }

    populateReceiptItems(items) {
        const itemsContainer = document.querySelector('.receipt-items');
        if (!itemsContainer || !items) return;

        let itemsHtml = '';
        items.forEach(item => {
            const itemTotal = item.price * item.quantity;
            itemsHtml += `
                <div class="d-flex justify-content-between py-1">
                    <div>
                        <small class="fw-bold">${item.name}</small>
                        <br>
                        <small class="text-muted">${item.quantity} × ${this.currency}${this.formatAmount(item.price)}</small>
                    </div>
                    <div class="text-end">
                        <small class="fw-bold">${this.currency}${this.formatAmount(itemTotal)}</small>
                    </div>
                </div>
            `;
        });
        itemsContainer.innerHTML = itemsHtml;
    }

    printReceipt() {
        const receiptData = this.getReceiptData();
        const printUrl = `print_receipt.php?data=${encodeURIComponent(JSON.stringify(receiptData))}`;
        
        // Open print window
        const printWindow = window.open(printUrl, '_blank', 'width=800,height=600');
        
        // Check if window was opened successfully
        if (!printWindow) {
            console.error('Failed to open print window. Popup may be blocked.');
            alert('Print window could not be opened. Please check if popups are blocked and try again.');
            return;
        }
        
        // Auto-trigger print dialog when window loads
        printWindow.onload = function() {
            printWindow.focus();
            printWindow.print();
            
            // Auto-close print window if enabled
            if (window.autoClosePrintWindow) {
                // Close window after a short delay to allow printing to complete
                setTimeout(() => {
                    printWindow.close();
                }, 2000);
            }
        };
        
        // Handle window load error
        printWindow.onerror = function() {
            console.error('Error loading print window');
            alert('Error loading print window. Please try again.');
        };
        
        // Listen for the print window to close or complete printing
        const checkClosed = setInterval(() => {
            if (printWindow.closed) {
                clearInterval(checkClosed);
                // Start new transaction after print window closes
                this.startNewTransaction();
            }
        }, 1000);
    }


    getReceiptData() {
        const modal = document.getElementById('receiptModal');
        return {
            transaction_id: modal.querySelector('.receipt-transaction-id')?.textContent || '',
            date: modal.querySelector('.receipt-date')?.textContent || '',
            time: modal.querySelector('.receipt-time')?.textContent || '',
            payment_method: modal.querySelector('.receipt-payment-method')?.textContent || '',
            subtotal: modal.querySelector('.receipt-subtotal')?.textContent || '',
            tax: modal.querySelector('.receipt-tax')?.textContent || '',
            total: modal.querySelector('.receipt-total')?.textContent || '',
            items: this.extractItemsFromReceipt(),
            company_name: window.POSConfig?.companyName || 'POS System',
            company_address: window.POSConfig?.companyAddress || ''
        };
    }

    extractItemsFromReceipt() {
        const items = [];
        const itemElements = document.querySelectorAll('.receipt-items .d-flex');
        
        itemElements.forEach(element => {
            const name = element.querySelector('small.fw-bold')?.textContent || '';
            const qty = element.querySelector('small.text-muted')?.textContent || '';
            const price = element.querySelector('.text-end small.fw-bold')?.textContent || '';
            
            if (name) {
                items.push({ name, qty, price });
            }
        });
        
        return items;
    }

    startNewTransaction() {
        // Clear cart without confirmation and without page reload
        // Clear session cart on server
        fetch('clear_cart.php', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Clear cart data in memory
                window.cartData = [];
                window.paymentTotals = { subtotal: 0, tax: 0, total: 0 };
                
                // Update cart display using global function
                if (typeof updateCartDisplay === 'function') {
                    updateCartDisplay([]);
                }
                
                // Reset payment processor data
                this.transactionData = [];
                this.paymentAmount = 0;
                
                // Hide receipt modal
                const receiptModal = bootstrap.Modal.getInstance(document.getElementById('receiptModal'));
                if (receiptModal) receiptModal.hide();
                
                // Reset payment modal if it's open
                const paymentModal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
                if (paymentModal) paymentModal.hide();
                
                // Reset payment processor state
                this.resetPaymentModal();
                
                console.log('New transaction started - cart cleared');
            } else {
                console.error('Error clearing cart:', data.error);
                // Fallback to page reload if clearing fails
                window.location.reload();
            }
        })
        .catch(error => {
            console.error('Error clearing cart:', error);
            // Fallback to page reload if clearing fails
            window.location.reload();
        });
    }

    cancelPayment() {
        this.resetPaymentModal();
        const paymentModal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
        if (paymentModal) paymentModal.hide();
    }

    cancelReceipt() {
        const receiptModal = bootstrap.Modal.getInstance(document.getElementById('receiptModal'));
        if (receiptModal) receiptModal.hide();
    }

    // LOYALTY POINTS METHODS

    async loadLoyaltySettings() {
        try {
            const response = await fetch('../api/get_loyalty_settings.php');
            const data = await response.json();
            
            if (data.success) {
                this.loyaltySettings = data.settings;
                this.updateLoyaltyConversionRate();
            } else {
                console.error('Failed to load loyalty settings:', data.error);
                // Set default settings
                this.loyaltySettings = {
                    points_per_currency: 100,
                    points_to_currency_rate: 100
                };
            }
        } catch (error) {
            console.error('Error loading loyalty settings:', error);
            // Set default settings
            this.loyaltySettings = {
                points_per_currency: 100,
                points_to_currency_rate: 100
            };
        }
    }

    updateLoyaltyConversionRate() {
        if (this.loyaltySettings) {
            const rate = this.loyaltySettings.points_to_currency_rate || 100;
            const conversionRateEl = document.getElementById('loyaltyConversionRate');
            if (conversionRateEl) {
                conversionRateEl.textContent = `${rate} points = ${this.currency}1.00`;
            }
        }
    }

    calculateLoyaltyPoints() {
        const amountInput = document.getElementById('loyaltyAmountToDeduct');
        const pointsRequiredEl = document.getElementById('loyaltyPointsRequired');
        const conversionStatusEl = document.getElementById('loyaltyConversionStatus');
        
        if (!amountInput || !pointsRequiredEl || !this.loyaltySettings) return;

        const amount = parseFloat(amountInput.value) || 0;
        const rate = this.loyaltySettings.points_to_currency_rate || 100;
        const pointsRequired = Math.ceil(amount * rate);

        // Update points display
        pointsRequiredEl.textContent = `${pointsRequired.toLocaleString()} points`;

        // Check if customer has enough points
        if (this.loyaltySelectedCustomer && this.loyaltySelectedCustomer.loyalty_points !== undefined) {
            const availablePoints = this.loyaltySelectedCustomer.loyalty_points;
            
            if (pointsRequired > availablePoints) {
                pointsRequiredEl.style.color = '#dc3545';
                conversionStatusEl.innerHTML = '<i class="bi bi-exclamation-triangle text-danger" style="font-size: 1.2rem;"></i>';
            } else {
                pointsRequiredEl.style.color = '#856404';
                conversionStatusEl.innerHTML = '<i class="bi bi-check-circle text-success" style="font-size: 1.2rem;"></i>';
            }
        } else {
            pointsRequiredEl.style.color = '#856404';
            conversionStatusEl.innerHTML = '<i class="bi bi-info-circle text-info" style="font-size: 1.2rem;"></i>';
        }

        // Update payment breakdown
        if (this.selectedPaymentMethods.has('loyalty_points')) {
            this.paymentBreakdown.set('loyalty_points', amount);
            this.updatePaymentAmounts();
            this.updatePaymentSummary();
            this.updateConfirmButton();
        }
    }

    handleLoyaltyQuickAmount(event) {
        const amount = parseFloat(event.currentTarget.dataset.amount) || 0;
        const loyaltyAmountInput = document.getElementById('loyaltyAmountToDeduct');
        if (loyaltyAmountInput) {
            loyaltyAmountInput.value = amount;
            try {
                loyaltyAmountInput.focus();
            } catch (error) {
                console.warn('Error focusing loyalty amount input:', error);
            }
            this.calculateLoyaltyPoints();
        }
    }

    setLoyaltyMaxAmount() {
        if (!this.loyaltySelectedCustomer || this.loyaltySelectedCustomer.loyalty_points === undefined) {
            alert('Please select a customer first');
            return;
        }

        const availablePoints = this.loyaltySelectedCustomer.loyalty_points;
        const rate = this.loyaltySettings?.points_to_currency_rate || 100;
        const maxAmount = availablePoints / rate;
        const transactionAmount = this.paymentAmount;
        const maxDeductible = Math.min(maxAmount, transactionAmount);

        const loyaltyAmountInput = document.getElementById('loyaltyAmountToDeduct');
        if (loyaltyAmountInput) {
            loyaltyAmountInput.value = this.formatAmount(maxDeductible);
            try {
                loyaltyAmountInput.focus();
            } catch (error) {
                console.warn('Error focusing loyalty amount input:', error);
            }
            this.calculateLoyaltyPoints();
        }
    }

    async loadCustomerLoyaltyInfo() {
        // Ensure the loyalty payment section is visible first
        const loyaltySection = document.getElementById('loyaltyPointsPaymentSection');
        if (loyaltySection) {
            loyaltySection.style.display = 'block';
        }

        // Load loyalty settings from the correct endpoint
        try {
            const response = await fetch('../api/get_loyalty_settings.php');
            const data = await response.json();
            
            if (data.success) {
                this.loyaltySettings = data.settings;
                this.updateLoyaltyConversionRate();
            } else {
                console.error('Failed to load loyalty settings:', data.error);
                // Use default settings from loyalty-points.php
                this.loyaltySettings = {
                    points_to_currency_rate: 100,
                    minimum_redemption_points: 100,
                    points_per_currency: 1
                };
            }
        } catch (error) {
            console.error('Error loading loyalty settings:', error);
            // Use default settings from loyalty-points.php
            this.loyaltySettings = {
                points_to_currency_rate: 100,
                minimum_redemption_points: 100,
                points_per_currency: 1
            };
        }
        
        // Check if we have a selected customer from the main customer selection
        const selectedCustomer = window.selectedCustomer;
        if (selectedCustomer && selectedCustomer.customer_type !== 'walk_in') {
            // Use the already selected customer
            this.loyaltySelectedCustomer = selectedCustomer;
            this.loadLoyaltyDataForCustomer(selectedCustomer.id);
        } else {
            // Show customer search interface
            this.showLoyaltyCustomerSearch();
        }
    }

    showLoyaltyCustomerSearch() {
        // Clear any previous selection
        this.loyaltySelectedCustomer = null;
        
        // Use setTimeout to ensure DOM elements are available
        setTimeout(() => {
            const loyaltySelectedCustomerEl = document.getElementById('loyaltySelectedCustomer');
            const loyaltyCustomerResultsEl = document.getElementById('loyaltyCustomerResults');
            const loyaltyCustomerPointsInfo = document.getElementById('loyaltyCustomerPointsInfo');
            const loyaltyAmountSection = document.getElementById('loyaltyAmountSection');
            const loyaltyQuickAmounts = document.getElementById('loyaltyQuickAmounts');
            
            if (loyaltySelectedCustomerEl) {
                loyaltySelectedCustomerEl.style.display = 'none';
            }
            if (loyaltyCustomerResultsEl) {
                loyaltyCustomerResultsEl.style.display = 'none';
            }
            
            // Hide amount section and related elements
            if (loyaltyCustomerPointsInfo) {
                loyaltyCustomerPointsInfo.style.display = 'none';
            }
            if (loyaltyAmountSection) {
                loyaltyAmountSection.style.display = 'none';
            }
            if (loyaltyQuickAmounts) {
                loyaltyQuickAmounts.style.display = 'none';
            }
            
            // Clear and disable amount input
            const loyaltyAmountInput = document.getElementById('loyaltyAmountToDeduct');
            if (loyaltyAmountInput) {
                try {
                    loyaltyAmountInput.value = '';
                    loyaltyAmountInput.disabled = true;
                } catch (error) {
                    console.warn('Error setting loyalty amount input properties:', error);
                }
            }
            
            // Reset points display
            const pointsRequiredEl = document.getElementById('loyaltyPointsRequired');
            if (pointsRequiredEl) {
                pointsRequiredEl.textContent = '0 points';
                pointsRequiredEl.style.color = '#856404';
            }
            
            const conversionStatusEl = document.getElementById('loyaltyConversionStatus');
            if (conversionStatusEl) {
                conversionStatusEl.innerHTML = '<i class="bi bi-info-circle text-info" style="font-size: 1.2rem;"></i>';
            }
        }, 50);
    }

    // Cash Customer Search Methods
    showCashCustomerSearch() {
        // Clear any previous selection
        this.cashSelectedCustomer = null;
        
        const cashSelectedCustomerEl = document.getElementById('cashSelectedCustomer');
        const cashCustomerResultsEl = document.getElementById('cashCustomerResults');
        const cashLoyaltyPointsInfoEl = document.getElementById('cashLoyaltyPointsInfo');
        
        if (cashSelectedCustomerEl) {
            cashSelectedCustomerEl.style.display = 'none';
        }
        if (cashCustomerResultsEl) {
            cashCustomerResultsEl.style.display = 'none';
        }
        if (cashLoyaltyPointsInfoEl) {
            cashLoyaltyPointsInfoEl.style.display = 'none';
        }
    }

    async searchCashCustomers(searchTerm) {
        try {
            const response = await fetch(`../api/search_customers_loyalty.php?search=${encodeURIComponent(searchTerm)}`);
            const data = await response.json();

            if (data.success) {
                this.displayCashCustomerResults(data.customers);
            } else {
                this.showCashError(data.error || 'Failed to search customers');
            }
        } catch (error) {
            console.error('Error searching customers:', error);
            this.showCashError('Failed to search customers');
        }
    }

    displayCashCustomerResults(customers) {
        const resultsDiv = document.getElementById('cashCustomerResults');
        
        if (customers.length === 0) {
            resultsDiv.innerHTML = '<div class="alert alert-info">No customers found</div>';
        } else {
            let html = '<div class="list-group">';
            customers.forEach(customer => {
                html += `
                    <div class="list-group-item list-group-item-action" onclick="window.paymentProcessor.selectCashCustomer(${customer.id}, '${customer.display_name}', '${customer.membership_level}')">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">${customer.display_name}</h6>
                            <small class="text-muted">${customer.membership_level}</small>
                        </div>
                        <p class="mb-1 text-muted">${customer.customer_number}</p>
                    </div>
                `;
            });
            html += '</div>';
            resultsDiv.innerHTML = html;
        }
        
        resultsDiv.style.display = 'block';
    }

    selectCashCustomer(customerId, customerName, membershipLevel) {
        this.cashSelectedCustomer = {
            id: customerId,
            display_name: customerName,
            membership_level: membershipLevel
        };
        
        // Hide search results
        document.getElementById('cashCustomerResults').style.display = 'none';
        
        // Show selected customer
        const cashSelectedCustomerName = document.getElementById('cashSelectedCustomerName');
        const cashSelectedCustomerPoints = document.getElementById('cashSelectedCustomerPoints');
        const cashSelectedCustomer = document.getElementById('cashSelectedCustomer');
        
        if (cashSelectedCustomerName) {
            cashSelectedCustomerName.textContent = customerName;
        }
        if (cashSelectedCustomerPoints) {
            cashSelectedCustomerPoints.textContent = `${membershipLevel} member`;
        }
        if (cashSelectedCustomer) {
            cashSelectedCustomer.style.display = 'block';
        }
        
        // Update loyalty points calculation
        this.updateCashLoyaltyPoints();
    }

    updateCashLoyaltyPoints() {
        if (this.cashSelectedCustomer && this.paymentAmount > 0) {
            // Calculate loyalty points that would be earned
            const pointsToEarn = this.calculateLoyaltyPointsForAmount(this.paymentAmount, this.cashSelectedCustomer.membership_level);
            const cashPointsToEarn = document.getElementById('cashPointsToEarn');
            const cashLoyaltyPointsInfo = document.getElementById('cashLoyaltyPointsInfo');
            
            if (cashPointsToEarn) {
                cashPointsToEarn.textContent = pointsToEarn;
            }
            if (cashLoyaltyPointsInfo) {
                cashLoyaltyPointsInfo.style.display = 'block';
            }
        } else {
            const cashLoyaltyPointsInfo = document.getElementById('cashLoyaltyPointsInfo');
            if (cashLoyaltyPointsInfo) {
                cashLoyaltyPointsInfo.style.display = 'none';
            }
        }
    }

    calculateLoyaltyPointsForAmount(amount, membershipLevel) {
        // This is a simplified calculation - in a real implementation, 
        // you'd want to call the server to get the actual loyalty settings
        const basePointsPerDollar = 1; // Default rate
        const multiplier = this.getMembershipMultiplier(membershipLevel);
        return Math.floor(amount * basePointsPerDollar * multiplier);
    }

    getMembershipMultiplier(level) {
        const multipliers = {
            'Basic': 1.0,
            'Silver': 1.5,
            'Gold': 2.0,
            'Platinum': 2.5,
            'Diamond': 3.0
        };
        return multipliers[level] || 1.0;
    }

    showCashError(message) {
        const resultsDiv = document.getElementById('cashCustomerResults');
        resultsDiv.innerHTML = `<div class="alert alert-danger">${message}</div>`;
        resultsDiv.style.display = 'block';
    }

    bindQuickAmountButtons() {
        // Re-bind quick amount buttons (in case they were dynamically added)
        const quickAmountBtns = document.querySelectorAll('.quick-amount');
        quickAmountBtns.forEach(btn => {
            // Remove existing event listeners to avoid duplicates
            btn.removeEventListener('click', this.handleQuickAmountClick);
            // Add new event listener
            btn.addEventListener('click', this.handleQuickAmountClick.bind(this));
        });

        // Re-bind exact amount button
        const exactAmountBtn = document.getElementById('exactAmountBtn');
        if (exactAmountBtn) {
            exactAmountBtn.removeEventListener('click', this.handleExactAmountClick);
            exactAmountBtn.addEventListener('click', this.handleExactAmountClick.bind(this));
        }
    }

    handleQuickAmountClick(event) {
        const amount = parseFloat(event.currentTarget.dataset.amount) || 0;
        const cashInput = document.getElementById('cashReceived');
        if (cashInput) {
            cashInput.value = amount;
            try {
                cashInput.focus();
            } catch (error) {
                console.warn('Error focusing cash input:', error);
            }
            this.calculateChange();
            this.updateCashLoyaltyPoints();
        }
    }

    handleExactAmountClick() {
        const cashInput = document.getElementById('cashReceived');
        if (cashInput) {
            cashInput.value = this.paymentAmount;
            try {
                cashInput.focus();
            } catch (error) {
                console.warn('Error focusing cash input:', error);
            }
            this.calculateChange();
            this.updateCashLoyaltyPoints();
        }
    }

    async searchLoyaltyCustomers(searchTerm) {
        try {
            const response = await fetch(`../api/search_customers_loyalty.php?search=${encodeURIComponent(searchTerm)}`);
            const data = await response.json();

            if (data.success) {
                this.displayLoyaltyCustomerResults(data.customers);
            } else {
                this.showLoyaltyError(data.error || 'Failed to search customers');
            }
        } catch (error) {
            console.error('Error searching customers:', error);
            this.showLoyaltyError('Failed to search customers');
        }
    }

    displayLoyaltyCustomerResults(customers) {
        const resultsDiv = document.getElementById('loyaltyCustomerResults');
        
        if (!resultsDiv) return;
        
        if (customers.length === 0) {
            resultsDiv.innerHTML = '<div class="alert alert-info">No customers found</div>';
        } else {
            let html = '<div class="list-group">';
            customers.forEach(customer => {
                html += `
                    <div class="list-group-item list-group-item-action" onclick="window.paymentProcessor.selectLoyaltyCustomer(${customer.id}, '${customer.display_name}', '${customer.membership_level}', ${customer.loyalty_points})">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">${customer.display_name}</h6>
                            <small class="text-muted">${customer.membership_level}</small>
                        </div>
                        <p class="mb-1 text-muted">${customer.customer_number}</p>
                    </div>
                `;
            });
            html += '</div>';
            resultsDiv.innerHTML = html;
        }
        
        resultsDiv.style.display = 'block';
    }

    selectLoyaltyCustomer(customerId, customerName, membershipLevel, loyaltyPoints) {
        this.loyaltySelectedCustomer = {
            id: customerId,
            display_name: customerName,
            membership_level: membershipLevel,
            loyalty_points: loyaltyPoints
        };
        
        // Hide search results
        const loyaltyCustomerResultsEl = document.getElementById('loyaltyCustomerResults');
        if (loyaltyCustomerResultsEl) {
            loyaltyCustomerResultsEl.style.display = 'none';
        }
        
        // Show selected customer
        const loyaltySelectedCustomerNameEl = document.getElementById('loyaltySelectedCustomerName');
        const loyaltySelectedCustomerInfoEl = document.getElementById('loyaltySelectedCustomerInfo');
        const loyaltySelectedCustomerEl = document.getElementById('loyaltySelectedCustomer');
        
        if (loyaltySelectedCustomerNameEl) {
            try {
                loyaltySelectedCustomerNameEl.textContent = customerName;
            } catch (error) {
                console.warn('Error setting loyalty selected customer name:', error);
            }
        }
        if (loyaltySelectedCustomerInfoEl) {
            try {
                loyaltySelectedCustomerInfoEl.textContent = `${membershipLevel} member`;
            } catch (error) {
                console.warn('Error setting loyalty selected customer info:', error);
            }
        }
        if (loyaltySelectedCustomerEl) {
            loyaltySelectedCustomerEl.style.display = 'block';
        }
        
        // Show customer points info and amount section
        const loyaltyCustomerPointsInfo = document.getElementById('loyaltyCustomerPointsInfo');
        const loyaltyAmountSection = document.getElementById('loyaltyAmountSection');
        const loyaltyQuickAmounts = document.getElementById('loyaltyQuickAmounts');
        
        if (loyaltyCustomerPointsInfo) {
            loyaltyCustomerPointsInfo.style.display = 'block';
        }
        if (loyaltyAmountSection) {
            loyaltyAmountSection.style.display = 'block';
        }
        if (loyaltyQuickAmounts) {
            loyaltyQuickAmounts.style.display = 'block';
        }
        
        // Load loyalty data for the customer
        this.loadLoyaltyDataForCustomer(customerId);
    }

    async loadLoyaltyDataForCustomer(customerId) {
        try {
            const response = await fetch(`../api/get_customer_loyalty.php?customer_id=${customerId}`);
            const data = await response.json();

            if (data.success) {
                this.updateLoyaltyDisplay(data.loyalty);
            } else {
                this.showLoyaltyError(data.error || 'Failed to load loyalty information');
            }
        } catch (error) {
            console.error('Error loading loyalty info:', error);
            this.showLoyaltyError('Failed to load loyalty information');
        }
    }

    updateLoyaltyDisplay(loyaltyData) {
        // Update the customer's loyalty points
        if (this.loyaltySelectedCustomer) {
            this.loyaltySelectedCustomer.loyalty_points = loyaltyData.balance;
        }
        
        // Update customer points info display
        const loyaltyCustomerPointsBalance = document.getElementById('loyaltyCustomerPointsBalance');
        const loyaltyMaxDeductible = document.getElementById('loyaltyMaxDeductible');
        
        if (loyaltyCustomerPointsBalance) {
            loyaltyCustomerPointsBalance.textContent = loyaltyData.balance || 0;
        }
        
        // Calculate maximum deductible amount
        if (this.loyaltySettings && loyaltyData.balance) {
            const conversionRate = this.loyaltySettings.points_to_currency_rate || 100;
            const maxDeductible = (loyaltyData.balance / conversionRate);
            const transactionAmount = this.paymentAmount;
            const actualMaxDeductible = Math.min(maxDeductible, transactionAmount);
            
            if (loyaltyMaxDeductible) {
                loyaltyMaxDeductible.textContent = `${this.currency}${this.formatAmount(actualMaxDeductible)}`;
            }
        } else {
            if (loyaltyMaxDeductible) {
                loyaltyMaxDeductible.textContent = `${this.currency}0.00`;
            }
        }
        
        // Clear the amount input
        const loyaltyAmountInput = document.getElementById('loyaltyAmountToDeduct');
        if (loyaltyAmountInput) {
            loyaltyAmountInput.value = '';
            loyaltyAmountInput.disabled = false;
        }
        
        // Reset the points display
        const pointsRequiredEl = document.getElementById('loyaltyPointsRequired');
        if (pointsRequiredEl) {
            pointsRequiredEl.textContent = '0 points';
            pointsRequiredEl.style.color = '#856404';
        }
        
        const conversionStatusEl = document.getElementById('loyaltyConversionStatus');
        if (conversionStatusEl) {
            conversionStatusEl.innerHTML = '<i class="bi bi-info-circle text-info" style="font-size: 1.2rem;"></i>';
        }
    }

    handleLoyaltyPointsInput(event) {
        const pointsToUse = parseInt(event.target.value) || 0;
        const availablePoints = parseInt(document.getElementById('loyaltyPointsAvailable').value) || 0;
        
        // Validate points
        if (pointsToUse > availablePoints) {
            event.target.value = availablePoints;
            pointsToUse = availablePoints;
        }

        // Calculate points value (100 points = 1 currency unit)
        const pointsValue = pointsToUse / 100;
        const remainingAmount = Math.max(0, this.paymentAmount - pointsValue);

        document.getElementById('loyaltyPointsValue').value = this.formatAmount(pointsValue);
        document.getElementById('remainingAmount').value = this.formatAmount(remainingAmount);

        // Update confirm button state
        this.updateConfirmButton();
    }

    showLoyaltyError(message) {
        const loyaltySection = document.getElementById('loyaltyPointsPaymentSection');
        if (loyaltySection) {
            loyaltySection.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Error:</strong> ${message}
                </div>
            `;
        }
    }

    validateLoyaltyPayment() {
        const selectedCustomer = this.loyaltySelectedCustomer;
        if (!selectedCustomer) {
            return { valid: false, error: 'Please select a customer for loyalty points payment' };
        }

        const amountToDeduct = parseFloat(document.getElementById('loyaltyAmountToDeduct').value) || 0;
        
        if (amountToDeduct <= 0) {
            return { valid: false, error: 'Please enter amount to deduct' };
        }

        if (amountToDeduct > this.paymentAmount) {
            return { valid: false, error: 'Amount to deduct cannot exceed transaction total' };
        }

        // Calculate points required
        const rate = this.loyaltySettings?.points_to_currency_rate || 100;
        const pointsRequired = Math.ceil(amountToDeduct * rate);
        const availablePoints = selectedCustomer.loyalty_points || 0;

        if (pointsRequired > availablePoints) {
            return { valid: false, error: 'Not enough loyalty points available' };
        }

        return { 
            valid: true, 
            pointsToUse: pointsRequired, 
            pointsValue: amountToDeduct, 
            remainingAmount: this.paymentAmount - amountToDeduct,
            customer: selectedCustomer
        };
    }

    resetPaymentModal() {
        // Reset payment method selection
        this.selectedPaymentMethods.clear();
        this.paymentBreakdown.clear();
        this.totalPaid = 0;
        this.remainingBalance = this.paymentAmount;
        
        document.querySelectorAll('.payment-method').forEach(method => {
            method.classList.remove('selected');
            method.style.transform = 'translateY(0)';
            method.style.boxShadow = '';
            method.style.borderColor = '';
        });
        
        // Uncheck all checkboxes
        document.querySelectorAll('.payment-method-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });

        // Reset collapsible state
        const collapseElement = document.getElementById('otherPaymentMethods');
        const collapseBtn = document.querySelector('[data-bs-target="#otherPaymentMethods"]');
        if (collapseElement && collapseBtn) {
            const bsCollapse = bootstrap.Collapse.getInstance(collapseElement);
            if (bsCollapse) {
                bsCollapse.hide();
            }
            const icon = collapseBtn.querySelector('i');
            if (icon) {
                icon.className = 'bi bi-chevron-down me-1';
            }
            collapseBtn.setAttribute('aria-expanded', 'false');
        }

        // Hide all payment sections
        document.getElementById('cashPaymentSection').style.display = 'none';
        document.getElementById('mobileMoneySection').style.display = 'none';
        document.getElementById('cardPaymentSection').style.display = 'none';
        document.getElementById('loyaltyPointsPaymentSection').style.display = 'none';

        // Clear form inputs
        const cashReceivedEl = document.getElementById('cashReceived');
        const mobileNumberEl = document.getElementById('mobileNumber');
        const mobileProviderEl = document.getElementById('mobileProvider');
        const cardNumberEl = document.getElementById('cardNumber');
        const cardExpiryEl = document.getElementById('cardExpiry');
        const cardCVVEl = document.getElementById('cardCVV');
        const loyaltyAmountEl = document.getElementById('loyaltyAmountToDeduct');
        const paymentNotesEl = document.getElementById('paymentNotes');
        
        if (cashReceivedEl) cashReceivedEl.value = '';
        if (mobileNumberEl) mobileNumberEl.value = '';
        if (mobileProviderEl) mobileProviderEl.value = '';
        if (cardNumberEl) cardNumberEl.value = '';
        if (cardExpiryEl) cardExpiryEl.value = '';
        if (cardCVVEl) cardCVVEl.value = '';
        if (loyaltyAmountEl) loyaltyAmountEl.value = '';
        if (paymentNotesEl) paymentNotesEl.value = '';

        // Reset change display
        const changeAmount = document.getElementById('changeAmount');
        if (changeAmount) {
            changeAmount.textContent = `${this.currency}0.00`;
        }
        const changeDisplay = document.getElementById('changeDisplay');
        if (changeDisplay) {
            changeDisplay.classList.remove('positive', 'negative');
        }

        // Reset payment summary
        this.updatePaymentSummary();
        this.updatePaymentBreakdown();
        
        // Disable confirm button
        const confirmBtn = document.querySelector('.payment-btn.confirm');
        if (confirmBtn) {
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>Confirm Payment';
        }
    }

    showError(message) {
        alert(`Error: ${message}`);
    }

    generateTransactionId() {
        // For now, use simple format. In production, this should be generated by the server
        const prefix = 'TXN';
        // Use mixed characters (numbers + uppercase + lowercase) for more variety
        const characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        let randomString = '';
        for (let i = 0; i < 6; i++) {
            randomString += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return `${prefix}${randomString}`;
    }

    formatPaymentMethod(method) {
        const methods = {
            cash: 'Cash',
            mobile_money: 'Mobile Money',
            credit_card: 'Credit Card',
            debit_card: 'Debit Card',
            pos_card: 'POS Card',
            bank_transfer: 'Bank Transfer',
            check: 'Check',
            online_payment: 'Online Payment',
            voucher: 'Voucher',
            store_credit: 'Store Credit'
        };
        return methods[method] || method;
    }

    formatAmount(amount) {
        const num = parseFloat(amount);
        if (isNaN(num) || !isFinite(num)) {
            return '0.00';
        }
        return num.toFixed(2);
    }
}

// Initialize payment processor when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.paymentProcessor = new PaymentProcessor();
});

// Expose to global scope for external access
window.PaymentProcessor = PaymentProcessor;
