/* Tax Management Styles */
:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --light-gray: #f8fafc;
    --border-color: #e2e8f0;
    --text-color: #374151;
    --text-muted: #6b7280;
    --sidebar-width: 250px;
    --border-radius: 8px;
    --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Main Layout */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    background-color: var(--light-gray);
    padding: 0;
}

/* Header */
.header {
    background: white;
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-title h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
}

.header-subtitle {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Content */
.content {
    padding: 0 2rem 2rem;
}

/* Cards */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.card-header {
    background: #f8fafc;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.card-body {
    padding: 1.5rem;
}

/* Tax Category Cards */
.tax-category-card {
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: white;
    box-shadow: var(--shadow);
    transition: all 0.2s ease;
}

.tax-category-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

/* Tax Rate Items */
.tax-rate-item {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: #f9fafb;
}

.tax-rate-item.active {
    background: #ecfdf5;
    border-color: var(--success-color);
}

.tax-rate-item.inactive {
    background: #fef2f2;
    border-color: var(--danger-color);
}

/* Badges */
.rate-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.875rem;
}

.compound-badge {
    background: var(--warning-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-active {
    background: #dcfce7;
    color: #166534;
}

.status-inactive {
    background: #fee2e2;
    color: #991b1b;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: #4f46e5;
    border-color: #4f46e5;
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
}

.btn-secondary {
    background: #6b7280;
    color: white;
    border-color: #6b7280;
}

.btn-secondary:hover {
    background: #4b5563;
    border-color: #4b5563;
}

/* Forms */
.form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.form-text {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: #f8fafc;
    font-weight: 600;
    color: var(--text-color);
}

.table-hover tbody tr:hover {
    background: #f8fafc;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background: #ecfdf5;
    border-color: #10b981;
    color: #065f46;
}

.alert-danger {
    background: #fef2f2;
    border-color: #ef4444;
    color: #991b1b;
}

.alert-info {
    background: #eff6ff;
    border-color: #3b82f6;
    color: #1e40af;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state i {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.empty-state h5 {
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
    }
    
    .header {
        padding: 1rem;
    }
    
    .content {
        padding: 0 1rem 1rem;
    }
    
    .header-content {
        flex-direction: column;
        align-items: stretch;
    }
    
    .header-actions {
        justify-content: center;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .tax-category-card {
        padding: 1rem;
    }
}

/* Modal Styles */
.modal-header {
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-title {
    font-weight: 600;
}

.btn-close-white {
    filter: invert(1);
}

/* Dropdown */
.dropdown-menu {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dropdown-item:hover {
    background: #f8fafc;
    color: var(--text-color);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-muted {
    color: var(--text-muted);
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 1rem;
}

.d-flex {
    display: flex;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}

.gap-3 {
    gap: 1rem;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.col-md-3,
.col-md-6,
.col-12 {
    padding: 0 0.75rem;
    flex: 1;
}

.col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

.col-12 {
    flex: 0 0 100%;
    max-width: 100%;
}

@media (max-width: 768px) {
    .col-md-3,
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}
