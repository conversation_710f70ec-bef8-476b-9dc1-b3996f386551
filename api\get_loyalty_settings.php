<?php
// Check if session is already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../include/db.php';
require_once __DIR__ . '/../include/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get loyalty settings from the settings table (same as loyalty-points.php)
    $stmt = $conn->query("SELECT setting_key, setting_value FROM settings");
    $settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    
    // Extract relevant loyalty settings
    $loyaltySettings = [
        'points_to_currency_rate' => floatval($settings['points_to_currency_rate'] ?? 100),
        'minimum_redemption_points' => intval($settings['minimum_redemption_points'] ?? 100),
        'points_per_currency' => floatval($settings['points_per_currency'] ?? 1),
        'enable_loyalty_program' => ($settings['loyalty_program_enabled'] ?? '1') === '1',
        'include_tax_in_calculation' => ($settings['include_tax_in_calculation'] ?? '1') === '1'
    ];
    
    echo json_encode([
        'success' => true,
        'settings' => $loyaltySettings
    ]);

} catch (Exception $e) {
    error_log("Error getting loyalty settings: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load loyalty settings'
    ]);
}
?>
