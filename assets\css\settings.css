/* Settings Management Styles */
:root {
    --primary-color: #6366f1;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-color: #e2e8f0;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --border-radius: 8px;
    --transition: all 0.2s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f1f5f9;
    color: var(--dark-color);
    line-height: 1.6;
}

/* Layout */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 260px;
    height: 100vh;
    background: linear-gradient(135deg, var(--primary-color) 0%, #4f46e5 100%);
    color: white;
    overflow-y: auto;
    z-index: 1000;
    transition: var(--transition);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h4 {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.sidebar-header small {
    opacity: 0.8;
    font-size: 0.875rem;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.15);
    border-right: 3px solid white;
}

.nav-link i {
    width: 20px;
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.main-content {
    margin-left: 260px;
    min-height: 100vh;
    background-color: #f1f5f9;
}

/* Header */
.header {
    background: white;
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
}

.header-title h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.header-subtitle {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Content */
.content {
    padding: 2rem;
}

/* Settings Navigation */
.settings-navigation {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.nav-tabs {
    border-bottom: none;
    padding: 0;
    background: var(--light-color);
}

.nav-tabs .nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 0;
    color: var(--secondary-color);
    background: transparent;
    transition: var(--transition);
    text-decoration: none;
    font-weight: 500;
}

.nav-tabs .nav-link:hover {
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background: white;
    border-bottom: 3px solid var(--primary-color);
}

.nav-tabs .nav-link i {
    margin-right: 0.5rem;
}

/* Settings Content */
.settings-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

/* Data Section */
.data-section {
    padding: 2rem;
}

.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    margin: 0;
}

/* Forms */
.settings-form {
    max-width: 800px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark-color);
    font-size: 0.875rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    transition: var(--transition);
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.form-control::placeholder {
    color: #9ca3af;
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

.input-group {
    display: flex;
    width: 100%;
}

.input-group .form-control {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.input-group-text {
    padding: 0.75rem;
    background: var(--light-color);
    border: 1px solid var(--border-color);
    border-left: none;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.invalid-feedback {
    display: block;
    color: var(--danger-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.form-text {
    color: var(--secondary-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Color Picker */
.color-picker-group {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.form-control-color {
    width: 60px;
    height: 45px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    flex-shrink: 0;
}

.color-picker-group .form-control {
    flex: 1;
}

/* Theme Preview */
.theme-preview {
    margin-top: 1rem;
}

.preview-card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    max-width: 300px;
}

.preview-header {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    transition: var(--transition);
}

.preview-header h6 {
    margin: 0;
    font-weight: 600;
}

.preview-body {
    padding: 1rem;
    background: white;
}

.preview-body p {
    margin-bottom: 0.75rem;
    color: var(--secondary-color);
    font-size: 0.875rem;
}

#previewButton {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    transition: var(--transition);
}

/* Checkboxes */
.form-check {
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.form-check-input {
    width: 18px;
    height: 18px;
    margin-right: 0.75rem;
    accent-color: var(--primary-color);
}

.form-check-label {
    font-size: 0.875rem;
    color: var(--dark-color);
    cursor: pointer;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #4f46e5;
    color: white;
}

.btn-outline-secondary {
    background: transparent;
    color: var(--secondary-color);
    border: 1px solid var(--border-color);
}

.btn-outline-secondary:hover {
    background: var(--secondary-color);
    color: white;
}

.btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* Utilities */
.d-flex {
    display: flex;
}

.align-items-center {
    align-items: center;
}

.justify-content-between {
    justify-content: space-between;
}

.gap-1 {
    gap: 0.25rem;
}

.gap-2 {
    gap: 0.5rem;
}

.gap-3 {
    gap: 1rem;
}

.text-center {
    text-align: center;
}

.text-muted {
    color: var(--secondary-color);
}

.me-2 {
    margin-right: 0.5rem;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

/* Row and Column Grid */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 0.75rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .header-content {
        padding: 1rem;
    }
    
    .content {
        padding: 1rem;
    }
    
    .data-section {
        padding: 1.5rem;
    }
    
    .nav-tabs {
        flex-direction: column;
    }
    
    .nav-tabs .nav-link {
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }
    
    .color-picker-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-control-color {
        width: 100%;
        height: 50px;
    }
    
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 1rem;
    }
    
    .settings-form {
        max-width: none;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .header-actions,
    .settings-navigation {
        display: none;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .content {
        padding: 0;
    }
}

/* Enhanced Form Elements */
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    appearance: none;
}

/* Number Input Styling */
input[type="number"].form-control {
    -moz-appearance: textfield;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Character Count Styling */
.char-count {
    font-size: 0.75rem;
    color: var(--secondary-color);
    margin-top: 0.25rem;
    text-align: right;
}

/* Form Check Enhancements */
.form-check {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.form-check-input {
    width: 1rem;
    height: 1rem;
    margin-top: 0.25rem;
    vertical-align: top;
    background-color: #fff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: 1px solid var(--border-color);
    appearance: none;
    border-radius: 0.25rem;
    transition: var(--transition);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
}

.form-check-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-left: 0.5rem;
    font-size: 0.875rem;
}

/* Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn .bi {
    transition: var(--transition);
}

/* Enhanced Color Picker */
.color-picker-group input[type="text"] {
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
}

/* Preview Card Enhancements */
.preview-body {
    padding: 1rem;
}

.preview-body p {
    margin-bottom: 1rem;
    color: var(--secondary-color);
    font-size: 0.875rem;
    line-height: 1.5;
}

#previewButton {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    transition: var(--transition);
    cursor: pointer;
}

#previewButton:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* Form Validation Enhancements */
.is-invalid {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Textarea Enhancements */
textarea.form-control {
    resize: vertical;
    min-height: 100px;
    line-height: 1.5;
}

/* Mobile Form Improvements */
@media (max-width: 576px) {
    .form-group .d-flex {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
}