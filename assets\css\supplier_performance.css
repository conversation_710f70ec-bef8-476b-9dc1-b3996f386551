/* Supplier Performance Styles */

/* Performance Dashboard Cards */
.performance-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.performance-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.performance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.performance-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e2e8f0;
}

.performance-card-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.performance-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

/* Overall Score Display */
.overall-score-container {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    color: white;
    margin-bottom: 2rem;
}

.overall-score-value {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.overall-score-label {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.overall-score-rating {
    display: inline-block;
    padding: 0.5rem 1.5rem;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Metric Cards */
.metric-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.metric-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.metric-change {
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.metric-change.positive {
    color: #059669;
}

.metric-change.negative {
    color: #dc2626;
}

.metric-change.neutral {
    color: #6b7280;
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 300px;
    margin: 2rem 0;
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e2e8f0;
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

/* Performance Table */
.performance-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.performance-table-header {
    background: #f8fafc;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.performance-table-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-excellent { background: #28a745; }
.status-good { background: #007bff; }
.status-fair { background: #ffc107; }
.status-poor { background: #fd7e14; }
.status-critical { background: #dc3545; }

/* Trend Indicators */
.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.trend-positive {
    background: #d1fae5;
    color: #059669;
}

.trend-negative {
    background: #fee2e2;
    color: #dc2626;
}

.trend-neutral {
    background: #f3f4f6;
    color: #6b7280;
}

/* Period Selector */
.period-selector {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.period-button {
    padding: 0.5rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #64748b;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.period-button:hover,
.period-button.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .performance-dashboard {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .overall-score-value {
        font-size: 3rem;
    }
}

@media (max-width: 768px) {
    .performance-dashboard {
        grid-template-columns: 1fr;
    }

    .metric-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .overall-score-container {
        padding: 1.5rem;
    }

    .overall-score-value {
        font-size: 2.5rem;
    }

    .period-selector {
        justify-content: center;
    }

    .chart-container {
        height: 250px;
    }

    .performance-card {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .metric-grid {
        grid-template-columns: 1fr;
    }

    .metric-value {
        font-size: 1.5rem;
    }

    .overall-score-value {
        font-size: 2rem;
    }

    .performance-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .performance-card-icon {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Tooltip Styles */
.performance-tooltip {
    position: relative;
    cursor: help;
}

.performance-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 0.5rem;
}

.performance-tooltip:hover::before {
    content: '';
    position: absolute;
    bottom: calc(100% - 0.25rem);
    left: 50%;
    transform: translateX(-50%);
    border: 0.25rem solid transparent;
    border-top-color: #333;
    z-index: 1000;
}

/* Print Styles */
@media print {
    .performance-card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .chart-container {
        page-break-inside: avoid;
    }

    .period-selector,
    .btn,
    .header-actions {
        display: none !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .performance-card {
        background: #1e293b;
        border-color: #334155;
        color: #f1f5f9;
    }

    .performance-card-header {
        border-color: #334155;
    }

    .performance-card-title {
        color: #f1f5f9;
    }

    .metric-label {
        color: #94a3b8;
    }

    .performance-table-header {
        background: #0f172a;
        border-color: #334155;
    }

    .performance-table-title {
        color: #f1f5f9;
    }

    .period-button {
        background: #1e293b;
        border-color: #334155;
        color: #94a3b8;
    }

    .period-button:hover,
    .period-button.active {
        background: var(--primary-color);
    }
}

/* Focus States for Accessibility */
.performance-card:focus-within,
.metric-card:focus-within,
.period-button:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .performance-card {
        border: 2px solid #000;
    }

    .metric-card {
        border: 2px solid #000;
    }

    .trend-indicator {
        border: 1px solid currentColor;
    }
}
