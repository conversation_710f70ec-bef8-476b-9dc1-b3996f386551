/* Modern POS System Styles */
:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --light-gray: #f8fafc;
    --medium-gray: #e2e8f0;
    --dark-gray: #64748b;
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --sidebar-color: #1e293b;
    --border-radius: 12px;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Base Styles */
.pos-container {
    display: flex;
    height: 100vh;
    background: var(--light-gray);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* POS Full Width Layout */
.pos-container-full {
    width: 100vw;
    height: 100vh;
    background: var(--light-gray);
    overflow: hidden;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.pos-main-content {
    width: 100%;
    height: 100vh;
    padding: 0;
    overflow: hidden;
}

/* Enhanced POS Top Bar */
.pos-top-bar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    padding: 0.75rem 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    position: sticky;
    top: 0;
    z-index: 999;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.pos-brand {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.pos-brand h5 {
    color: white !important;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.pos-brand h5 i {
    font-size: 1.25rem;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
    animation: pulse-icon 2s infinite;
}

@keyframes pulse-icon {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.pos-brand small {
    color: rgba(255,255,255,0.9);
    font-size: 0.8rem;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.pos-top-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Enhanced Dashboard Button */
.pos-dashboard-btn {
    background: rgba(255,255,255,0.15);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    font-weight: 600;
    border-radius: 10px;
    padding: 0.5rem 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-size: 0.85rem;
    position: relative;
    overflow: hidden;
}

.pos-dashboard-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.pos-dashboard-btn:hover::before {
    left: 100%;
}

.pos-dashboard-btn:hover {
    background: rgba(255,255,255,0.25);
    border-color: rgba(255,255,255,0.4);
    color: white;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.pos-dashboard-btn:active {
    transform: translateY(0) scale(0.98);
}

.pos-dashboard-btn i {
    font-size: 1.1rem;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
}

/* Enhanced User Info */
.pos-user-info {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    background: rgba(255,255,255,0.12);
    padding: 0.5rem 1rem;
    border-radius: 12px;
    border: 1px solid rgba(255,255,255,0.2);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    transition: all 0.3s ease;
    position: relative;
}

.pos-user-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    border-radius: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.pos-user-info:hover::before {
    opacity: 1;
}

.pos-user-info:hover {
    background: rgba(255,255,255,0.18);
    border-color: rgba(255,255,255,0.3);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.pos-user-info .user-avatar {
    width: 36px;
    height: 36px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.9rem;
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    position: relative;
    z-index: 1;
}

.pos-user-info .user-details {
    display: flex;
    flex-direction: column;
    gap: 0.1rem;
    position: relative;
    z-index: 1;
}

.pos-user-info .user-welcome {
    color: rgba(255,255,255,0.8);
    font-size: 0.7rem;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.pos-user-info .user-name {
    color: white;
    font-weight: 600;
    font-size: 0.85rem;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.pos-user-info .user-role {
    background: rgba(255,255,255,0.2);
    color: white;
    font-size: 0.65rem;
    font-weight: 600;
    padding: 0.25rem 0.6rem;
    border-radius: 8px;
    margin-left: 0.4rem;
    text-shadow: none;
    border: 1px solid rgba(255,255,255,0.3);
    position: relative;
    z-index: 1;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* DateTime Display */
.pos-datetime {
    display: flex;
    align-items: center;
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 10px;
    padding: 0.5rem 0.75rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.pos-datetime:hover {
    background: rgba(255,255,255,0.15);
    border-color: rgba(255,255,255,0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.datetime-display {
    display: flex;
    flex-direction: column;
    gap: 0.1rem;
    position: relative;
    z-index: 1;
}

.current-time,
.current-date {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.7rem;
    color: rgba(255,255,255,0.95);
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.current-time {
    font-weight: 600;
    letter-spacing: 0.3px;
}

.current-time i,
.current-date i {
    font-size: 0.75rem;
    opacity: 0.9;
}

.time-text {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.date-text {
    font-size: 0.65rem;
    font-weight: 500;
}

/* Logout Button */
.pos-logout-btn {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: rgba(255,255,255,0.9);
    padding: 0.6rem;
    border-radius: 50%;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    width: 40px;
    height: 40px;
    position: relative;
    overflow: hidden;
}

.pos-logout-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(239, 68, 68, 0.3), transparent);
    transition: left 0.5s;
}

.pos-logout-btn:hover::before {
    left: 100%;
}

.pos-logout-btn:hover {
    background: rgba(239, 68, 68, 0.9);
    border-color: rgba(239, 68, 68, 1);
    color: white;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.3);
}

.pos-logout-btn:active {
    transform: translateY(0) scale(0.95);
}

.pos-logout-btn i {
    font-size: 1rem;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));
    position: relative;
    z-index: 1;
}

/* Main Layout */
.pos-main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    height: calc(100vh - 100px);
    background: transparent;
    border-radius: 0;
    padding: 1rem 1.5rem;
    margin: 0;
    box-shadow: none;
    overflow: hidden;
}

/* Enhanced Products Section */
.products-section {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 30px rgba(0,0,0,0.08);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0,0,0,0.06);
}

.products-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #f1f5f9;
    flex-wrap: wrap;
    gap: 1rem;
}

.products-section-header h5 {
    color: #1e293b;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.products-section-header .bi {
    color: var(--primary-color);
    font-size: 1.3rem;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.products-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-input {
    position: relative;
}

.search-input input {
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    background: white;
    font-size: 0.9rem;
    width: 280px;
    transition: all 0.2s ease;
}

.search-input input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-input i {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--dark-gray);
}

/* Enhanced Search Input */
.search-input-enhanced {
    position: relative;
    flex: 1;
    max-width: 500px;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid var(--medium-gray);
    border-radius: var(--border-radius);
    padding: 4px;
    transition: all 0.2s ease;
}

.search-input-enhanced:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-input-enhanced i.bi-search {
    margin-left: 12px;
    color: var(--dark-gray);
    font-size: 16px;
}

.search-input-enhanced input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 12px 16px;
    font-size: 15px;
    color: var(--text-primary);
    outline: none;
}

.search-input-enhanced input::placeholder {
    color: var(--dark-gray);
    font-weight: 400;
}

.barcode-scan-btn {
    background: var(--primary-color);
    border: none;
    color: white;
    width: 44px;
    height: 44px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 4px;
}

.barcode-scan-btn:hover {
    background: var(--primary-color-dark, #5856eb);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.2);
}

.barcode-scan-btn:active {
    transform: translateY(0);
}

.barcode-scan-btn i {
    font-size: 18px;
}

/* Search Suggestions Dropdown */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e6ed;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.suggestions-list {
    padding: 0;
    margin: 0;
}

.suggestion-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.highlighted {
    background: var(--light-bg, #f8fafc);
}

.suggestion-item.selected {
    background: rgba(99, 102, 241, 0.1);
    border-left: 3px solid var(--primary-color);
}

.suggestion-icon {
    width: 32px;
    height: 32px;
    background: var(--light-bg, #f8fafc);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted, #64748b);
    font-size: 1.1rem;
    flex-shrink: 0;
}

.suggestion-content {
    flex: 1;
    min-width: 0;
}

.suggestion-name {
    font-weight: 600;
    color: var(--text-primary, #1e293b);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.suggestion-details {
    font-size: 0.85rem;
    color: var(--text-muted, #64748b);
    display: flex;
    gap: 8px;
    align-items: center;
}

.suggestion-price {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.9rem;
    margin-left: auto;
}

.suggestion-stock {
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 4px;
    background: #dcfce7;
    color: #16a34a;
}

.suggestion-stock.low-stock {
    background: #fef3c7;
    color: #d97706;
}

.suggestion-stock.out-of-stock {
    background: #fee2e2;
    color: #dc2626;
}

.no-suggestions {
    padding: 20px;
    text-align: center;
    color: var(--text-muted, #64748b);
    font-style: italic;
}

.no-suggestions i {
    font-size: 1.5rem;
    margin-bottom: 8px;
    display: block;
    opacity: 0.5;
}

/* Custom scrollbar for suggestions */
.search-suggestions::-webkit-scrollbar {
    width: 4px;
}

.search-suggestions::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.search-suggestions::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.search-suggestions::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

.category-select {
    padding: 0.75rem;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    background: white;
    font-size: 0.9rem;
    min-width: 150px;
}

.category-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Category Tabs */
.category-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;
}

.category-tab {
    padding: 0.75rem 1.5rem;
    background: var(--light-gray);
    border: 1px solid var(--medium-gray);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-size: 0.9rem;
    font-weight: 500;
}

.category-tab:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.category-tab.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.category-count {
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 6px;
    display: inline-block;
}

.category-tab:hover .category-count {
    background: rgba(255, 255, 255, 0.25);
}

.category-tab.active .category-count {
    background: rgba(255, 255, 255, 0.3);
}

/* Product Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 1rem;
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem;
    scrollbar-width: thin;
    scrollbar-color: var(--medium-gray) transparent;
}

.products-grid::-webkit-scrollbar {
    width: 6px;
}

.products-grid::-webkit-scrollbar-track {
    background: transparent;
}

.products-grid::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 3px;
}

/* Product Card */
.product-card {
    background: white;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    display: flex;
    flex-direction: column;
    height: 160px;
    justify-content: space-between;
}

.product-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.product-image {
    width: 50px;
    height: 50px;
    background: var(--light-gray);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
    color: var(--dark-gray);
    font-size: 1.5rem;
}

.product-name {
    font-weight: 600;
    font-size: 0.85rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 2.4em;
}

.product-price {
    font-weight: 700;
    font-size: 1rem;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.product-stock {
    font-size: 0.75rem;
    color: var(--dark-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.product-stock.low-stock {
    color: var(--warning-color);
}

.product-stock.out-of-stock {
    color: var(--danger-color);
}

/* Enhanced Cart Section */
.cart-section {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 30px rgba(0,0,0,0.08);
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.06);
}

.cart-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.cart-section-header h5 {
    color: #1e293b;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-section-header .bi {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.cart-header {
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid var(--medium-gray);
}

.cart-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.customer-info {
    background: var(--light-gray);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.customer-name {
    font-weight: 600;
    color: var(--text-primary);
}

.change-customer-btn {
    background: none;
    border: 1px solid var(--medium-gray);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.change-customer-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Compact Cart Items - Optimized to hold more products */
.cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 0.75rem;
    background: #fafbfc;
    scrollbar-width: thin;
    scrollbar-color: var(--medium-gray) transparent;
}

.cart-items::-webkit-scrollbar {
    width: 6px;
}

.cart-items::-webkit-scrollbar-track {
    background: transparent;
}

.cart-items::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 3px;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: white;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    border: 1px solid #f1f5f9;
    position: relative;
    min-height: 70px;
}

.cart-item:hover {
    background: #fefeff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-color: #e2e8f0;
}

.cart-item-image {
    width: 42px;
    height: 42px;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    color: var(--primary-color);
    border: 1px solid #f1f5f9;
    flex-shrink: 0;
}

.cart-item-info {
    flex: 1;
    min-width: 0;
    padding-right: 0.5rem;
}

.cart-item-name {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-height: 2.4em;
}

.cart-item-price {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.quantity-controls {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

.quantity-btn {
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    min-width: 32px;
    height: 32px;
}

.quantity-btn:hover {
    background: var(--primary-color);
    color: white;
}

.quantity-btn:active {
    transform: scale(0.95);
}

.quantity-display {
    padding: 0.5rem 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
    background: #fafbfc;
    border: none;
    border-left: 1px solid var(--medium-gray);
    border-right: 1px solid var(--medium-gray);
    min-width: 40px;
    text-align: center;
    font-size: 0.9rem;
}

.cart-item-total {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 0.95rem;
    min-width: 60px;
    text-align: right;
}

.remove-item-btn {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: var(--danger-color);
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    font-size: 0.9rem;
}

.remove-item-btn:hover {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
    transform: scale(1.05);
}

.remove-item-btn:active {
    transform: scale(0.95);
}

/* Compact Cart Summary */
.cart-summary {
    border-top: 1px solid var(--medium-gray);
    padding: 1rem;
    background: white;
}

.cart-totals {
    margin-bottom: 1rem;
    background: #f8fafc;
    padding: 0.75rem;
    border-radius: 8px;
}

.cart-total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.3rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.cart-total-row:last-child {
    margin-bottom: 0;
}

.cart-total-row.total {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
    border-top: 1px solid var(--medium-gray);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

/* Compact Cart Actions */
.cart-actions {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.3rem;
    margin-bottom: 0.75rem;
}

.cart-action-btn {
    padding: 0.5rem 0.25rem;
    border: 1px solid var(--medium-gray);
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.15rem;
    font-size: 0.7rem;
    font-weight: 500;
    text-decoration: none;
    color: var(--text-secondary);
    min-height: 50px;
}

.cart-action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

.cart-action-btn.clear {
    border-color: #fecaca;
    color: var(--danger-color);
    background: #fef2f2;
}

.cart-action-btn.clear:hover {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.cart-action-btn i {
    font-size: 1rem;
}

.cart-action-btn span {
    font-size: 0.65rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Tax Display Styling */
#cartMultipleTaxRows .cart-total-row {
    font-size: 0.8rem;
    padding-left: 0.5rem;
    border-left: 2px solid var(--medium-gray);
    margin-left: 0.5rem;
    background: rgba(99, 102, 241, 0.05);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.25rem;
}

.cart-item-price small.text-muted {
    font-size: 0.7rem;
    margin-left: 0.25rem;
    font-weight: 500;
}

/* Enhanced Checkout Button */
.checkout-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.3px;
}

.checkout-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.checkout-btn:hover::before {
    left: 100%;
}

.checkout-btn:hover {
    background: linear-gradient(135deg, #10b981, #047857);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 30px rgba(16, 185, 129, 0.4);
    color: white;
}

.checkout-btn:active {
    transform: translateY(0) scale(0.98);
}

.checkout-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.checkout-btn.processing {
    background: #6b7280;
    cursor: wait;
    transform: none;
}

.checkout-btn.processing::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

/* Empty Cart */
.empty-cart {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--dark-gray);
    text-align: center;
}

.empty-cart i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-cart p {
    font-size: 1rem;
    margin: 0;
}

/* Payment Modal */
.payment-modal .modal-dialog {
    max-width: 500px;
}

.payment-header {
    background: var(--primary-color);
    color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.payment-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0 0 0.5rem;
}

.payment-amount {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.payment-methods {
    padding: 1.5rem;
}

.payment-method {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.payment-method:hover {
    border-color: var(--primary-color);
    background: #f8fafc;
}

.payment-method.selected {
    border-color: var(--primary-color);
    background: #f0f9ff;
}

.payment-method-icon {
    width: 40px;
    height: 40px;
    background: var(--light-gray);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.payment-method-info h6 {
    margin: 0 0 0.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.payment-method-info p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.payment-actions {
    padding: 1.5rem;
    border-top: 1px solid var(--medium-gray);
    display: flex;
    gap: 1rem;
}

.payment-btn {
    flex: 1;
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
}

.payment-btn.cancel {
    background: white;
    color: var(--text-secondary);
}

.payment-btn.cancel:hover {
    background: var(--light-gray);
}

.payment-btn.confirm {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.payment-btn.confirm:hover {
    background: #059669;
}

/* Receipt Modal */
.receipt-modal {
    max-width: 400px;
    margin: 2rem auto;
}

.receipt-header {
    text-align: center;
    padding: 1.5rem;
    background: var(--success-color);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.receipt-content {
    padding: 1.5rem;
    background: white;
}

.receipt-shop-info {
    text-align: center;
    margin-bottom: 1.5rem;
}

.receipt-shop-name {
    font-size: 1.2rem;
    font-weight: 700;
    margin: 0 0 0.25rem;
}

.receipt-shop-address {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin: 0;
}

.receipt-transaction-info {
    margin-bottom: 1.5rem;
}

.receipt-info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
    font-size: 0.8rem;
}

.receipt-items {
    border-top: 1px dashed var(--medium-gray);
    border-bottom: 1px dashed var(--medium-gray);
    padding: 1rem 0;
    margin-bottom: 1rem;
}

.receipt-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.receipt-item-details {
    flex: 1;
}

.receipt-item-name {
    font-weight: 600;
}

.receipt-item-qty {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.receipt-totals {
    margin-bottom: 1.5rem;
}

.receipt-total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.receipt-total-row.final {
    font-weight: 700;
    font-size: 1rem;
    border-top: 1px solid var(--medium-gray);
    padding-top: 0.5rem;
    margin-top: 1rem;
}

.receipt-thank-you {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.receipt-actions {
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
}

.receipt-btn {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.receipt-btn.print {
    background: white;
    color: var(--text-primary);
}

.receipt-btn.print:hover {
    background: var(--light-gray);
}

.receipt-btn.download {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.receipt-btn.download:hover {
    background: #5856eb;
}

.receipt-btn.new-transaction {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
    width: 100%;
    margin-top: 0.5rem;
}

.receipt-btn.new-transaction:hover {
    background: #059669;
}

.receipt-btn.cancel {
    background: white;
    color: #dc3545;
    border-color: #dc3545;
}

.receipt-btn.cancel:hover {
    background: #dc3545;
    color: white;
}

/* Cash Payment Section */
.cash-payment-section {
    padding: 1.5rem;
    border-top: 1px solid var(--medium-gray);
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
}

.cash-payment-header {
    margin-bottom: 1rem;
}

.cash-payment-header h6 {
    color: var(--text-primary);
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cash-payment-header i {
    color: var(--success-color);
    font-size: 1.1rem;
}

.cash-payment-inputs {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cash-payment-inputs .input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cash-payment-inputs .form-control {
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
    border: none;
    padding: 0.75rem 1rem;
    background: white;
}

.cash-payment-inputs .form-control:focus {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    border-color: var(--success-color);
}

.cash-payment-inputs .input-group-text {
    background: var(--success-color);
    color: white;
    font-weight: 600;
    border: none;
    padding: 0.75rem 1rem;
}

.change-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 1rem 1.25rem;
    border-radius: var(--border-radius);
    border: 2px solid var(--medium-gray);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.change-label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.change-amount {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--success-color);
}

.change-display.positive {
    border-color: var(--success-color);
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.02));
}

.change-display.negative {
    border-color: var(--danger-color);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(239, 68, 68, 0.02));
}

.change-display.negative .change-amount {
    color: var(--danger-color);
}

.cash-received-error {
    color: var(--danger-color);
    font-size: 0.8rem;
    margin-top: 0.5rem;
    display: none;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .pos-main {
        grid-template-columns: 1fr 350px;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

@media (max-width: 768px) {
    .pos-main {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
    }
    
    .cart-section {
        border-left: none;
        border-top: 1px solid var(--medium-gray);
        max-height: 40vh;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.75rem;
    }
    
    .product-card {
        height: 140px;
        padding: 0.75rem;
    }
    
    .cart-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid var(--primary-color);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Success feedback */
.success-feedback {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--success-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 1000;
    animation: successFade 1.5s ease-out;
}

@keyframes successFade {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    20% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    80% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}

/* Product Card Interactive States */
.product-card.adding-to-cart {
    transform: scale(0.98);
    opacity: 0.8;
    pointer-events: none;
    transition: all 0.15s ease;
}

.product-card.just-added {
    border-color: var(--success-color) !important;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2), var(--shadow-md);
    transform: translateY(-1px);
}

/* Click feedback animation */
.product-card:active:not(.adding-to-cart) {
    transform: scale(0.95) translateY(1px);
    transition: transform 0.1s ease;
}

/* Quick Checkout Modal - Modern & Unique Design */
.quick-checkout-content {
    border: none;
    border-radius: 24px;
    box-shadow: 0 32px 64px rgba(0,0,0,0.12), 0 0 0 1px rgba(255,255,255,0.05);
    overflow: hidden;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    max-width: 650px;
    backdrop-filter: blur(20px);
    position: relative;
}

.quick-checkout-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
    z-index: 1;
}

.quick-checkout-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    padding: 24px 28px;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.quick-checkout-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.05"/><circle cx="25" cy="25" r="0.5" fill="%23ffffff" opacity="0.03"/><circle cx="75" cy="75" r="0.8" fill="%23ffffff" opacity="0.04"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
    pointer-events: none;
}

.quick-checkout-header::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: headerFloat 6s ease-in-out infinite;
}

@keyframes headerFloat {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    50% { transform: translate(-10px, -10px) rotate(180deg); }
}

.quick-checkout-title {
    font-size: 1.375rem;
    font-weight: 800;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quick-checkout-title i {
    font-size: 1.5rem;
    animation: lightningPulse 2s ease-in-out infinite;
}

@keyframes lightningPulse {
    0%, 100% { transform: scale(1); filter: brightness(1); }
    50% { transform: scale(1.1); filter: brightness(1.2); }
}

.quick-checkout-close {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 2;
    backdrop-filter: blur(10px);
}

.quick-checkout-close:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Modern Order Summary */
.checkout-order-summary.compact {
    padding: 28px;
    background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
    border-bottom: 1px solid #e2e8f0;
    position: relative;
}

.checkout-order-summary.compact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 28px;
    right: 28px;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
}

.checkout-order-summary.compact .summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.checkout-order-summary.compact .summary-header h6 {
    margin: 0;
    font-size: 1rem;
    font-weight: 700;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkout-order-summary.compact .summary-header h6 i {
    color: #6366f1;
    font-size: 1.125rem;
}

.checkout-order-summary.compact .item-count {
    font-size: 0.75rem;
    color: #6366f1;
    background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    border: 1px solid #a5b4fc;
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.1);
}

.summary-items.compact {
    margin-bottom: 20px;
}

.summary-items.compact .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: white;
    border-radius: 12px;
    border: 1px solid #f1f5f9;
    box-shadow: 0 2px 4px rgba(0,0,0,0.02);
    transition: all 0.3s ease;
}

.summary-items.compact .summary-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    border-color: #e2e8f0;
}

.summary-items.compact .summary-item:last-child {
    margin-bottom: 0;
}

.summary-items.compact .summary-item-info .summary-item-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: #0f172a;
    margin-bottom: 4px;
}

.summary-items.compact .summary-item-info .summary-item-details {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
}

.summary-items.compact .summary-item-total {
    font-size: 0.875rem;
    font-weight: 700;
    color: #6366f1;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid #bae6fd;
}

.summary-totals.compact {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.02);
}

.summary-totals.compact .total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 0.875rem;
    font-weight: 500;
}

.summary-totals.compact .total-row:last-child {
    margin-bottom: 0;
}

.summary-totals.compact .total-row.final-total {
    font-size: 1.125rem;
    font-weight: 800;
    color: #0f172a;
    padding-top: 12px;
    border-top: 2px solid #e2e8f0;
    margin-top: 8px;
}

.summary-totals.compact .total-amount {
    color: #6366f1;
    font-size: 1.25rem;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 900;
}

/* Modern Payment Methods */
.checkout-payment-section.compact {
    padding: 28px;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
}

.checkout-payment-section.compact .payment-section-header h6 {
    margin: 0 0 20px;
    font-size: 1rem;
    font-weight: 700;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkout-payment-section.compact .payment-section-header h6 i {
    color: #6366f1;
    font-size: 1.125rem;
}

.payment-methods-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.payment-method-card.compact-card {
    border: 2px solid #f1f5f9;
    border-radius: 16px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.payment-method-card.compact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(255,255,255,0.5), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.payment-method-card.compact-card:hover::before {
    opacity: 1;
}

.payment-method-card.compact-card:hover {
    border-color: #6366f1;
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 40px rgba(99, 102, 241, 0.15), 0 0 0 1px rgba(99, 102, 241, 0.1);
}

.payment-method-card.compact-card.selected {
    border-color: #6366f1;
    background: linear-gradient(145deg, #f0f9ff 0%, #e0f2fe 100%);
    box-shadow: 0 12px 24px rgba(99, 102, 241, 0.2), 0 0 0 1px #6366f1;
    transform: translateY(-2px);
}

.payment-method-card.compact-card.selected::after {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: #6366f1;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    animation: checkmarkPop 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes checkmarkPop {
    0% { transform: scale(0) rotate(-180deg); opacity: 0; }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

.payment-method-card.compact-card .payment-method-icon {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 1.75rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

.payment-method-card.compact-card .cash-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
}

.payment-method-card.compact-card .card-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

.payment-method-card.compact-card .mobile-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    box-shadow: 0 8px 16px rgba(245, 158, 11, 0.3);
}

.payment-method-card.compact-card .bank-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    box-shadow: 0 8px 16px rgba(139, 92, 246, 0.3);
}

.payment-method-card.compact-card .payment-method-info .method-name {
    font-size: 0.875rem;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 6px;
}

.payment-method-card.compact-card .payment-method-info .method-desc {
    font-size: 0.75rem;
    color: #64748b;
    line-height: 1.4;
    font-weight: 500;
}

.payment-method-card.compact-card.selected .payment-method-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 24px rgba(0,0,0,0.2);
}

/* Enhanced Cash Details Section */
.cash-details-section {
    padding: 28px;
    background: linear-gradient(145deg, #f0f9ff 0%, #e0f2fe 100%);
    border-top: 1px solid #bae6fd;
}

.cash-details-header h6 {
    margin: 0 0 20px;
    font-size: 1rem;
    font-weight: 700;
    color: #0369a1;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cash-input-group {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 20px;
    align-items: end;
    margin-bottom: 24px;
}

.amount-input label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #0369a1;
    margin-bottom: 8px;
    display: block;
}

.amount-input .input-group {
    position: relative;
}

.amount-input .input-group-text {
    background: linear-gradient(135deg, #0369a1, #0284c7);
    color: white;
    border: none;
    font-weight: 700;
    border-radius: 12px 0 0 12px;
}

.amount-input .form-control {
    border: 2px solid #bae6fd;
    border-left: none;
    border-radius: 0 12px 12px 0;
    padding: 12px 16px;
    font-size: 1.125rem;
    font-weight: 600;
    background: white;
    transition: all 0.3s ease;
}

.amount-input .form-control:focus {
    border-color: #0369a1;
    box-shadow: 0 0 0 3px rgba(3, 105, 161, 0.1);
    outline: none;
}

.change-calculation {
    text-align: center;
    background: white;
    padding: 16px;
    border-radius: 12px;
    border: 2px solid #bae6fd;
    min-width: 140px;
}

.change-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #0369a1;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.change-amount {
    font-size: 1.25rem;
    font-weight: 900;
    color: #059669;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.cash-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.cash-actions .btn {
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cash-actions .btn-outline-secondary {
    border: 2px solid #94a3b8;
    color: #475569;
}

.cash-actions .btn-outline-secondary:hover {
    background: #94a3b8;
    transform: translateY(-2px);
}

.cash-actions .btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    border: none;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.cash-actions .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
}

.cash-actions .btn-success:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: none;
}

/* Responsive Design for Modern Layout */
@media (max-width: 480px) {
    .payment-methods-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .quick-checkout-content {
        margin: 16px;
        max-width: none;
        border-radius: 20px;
    }
    
    .quick-checkout-header,
    .checkout-order-summary.compact,
    .checkout-payment-section.compact,
    .cash-details-section {
        padding: 20px;
    }
    
    .cash-input-group {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .change-calculation {
        min-width: auto;
    }
}

/* Modern Success Modal Styles */
.modern-success-content {
    border: none;
    border-radius: 24px;
    overflow: hidden;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    box-shadow: 0 32px 64px rgba(0,0,0,0.12), 0 0 0 1px rgba(255,255,255,0.1);
    backdrop-filter: blur(20px);
    max-width: 500px;
    margin: 0 auto;
}

.success-animation-header {
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
    padding: 40px 32px 32px;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.success-animation-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="successGrain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="25" cy="75" r="0.5" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23successGrain)"/></svg>') repeat;
    pointer-events: none;
}

.success-checkmark {
    display: inline-block;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.checkmark-circle {
    width: 80px;
    height: 80px;
    position: relative;
    display: inline-block;
    vertical-align: top;
}

.checkmark-circle::before {
    content: '';
    width: 80px;
    height: 80px;
    position: absolute;
    left: 0;
    top: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transform: scale(0);
    animation: checkmarkCircle 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.2s forwards;
}

.checkmark-stem,
.checkmark-kick {
    position: absolute;
    background: white;
    border-radius: 2px;
}

.checkmark-stem {
    width: 3px;
    height: 18px;
    top: 31px;
    left: 38px;
    transform: rotate(45deg);
    transform-origin: bottom;
    animation: checkmarkStem 0.3s ease-in-out 0.8s forwards;
    transform: rotate(45deg) scaleY(0);
}

.checkmark-kick {
    width: 3px;
    height: 12px;
    top: 42px;
    left: 30px;
    transform: rotate(-45deg);
    transform-origin: bottom;
    animation: checkmarkKick 0.3s ease-in-out 1.1s forwards;
    transform: rotate(-45deg) scaleY(0);
}

@keyframes checkmarkCircle {
    from {
        transform: scale(0);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes checkmarkStem {
    from {
        transform: rotate(45deg) scaleY(0);
    }
    to {
        transform: rotate(45deg) scaleY(1);
    }
}

@keyframes checkmarkKick {
    from {
        transform: rotate(-45deg) scaleY(0);
    }
    to {
        transform: rotate(-45deg) scaleY(1);
    }
}

.success-title {
    font-size: 1.75rem;
    font-weight: 800;
    margin: 0 0 8px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    animation: successTitleSlide 0.6s ease-out 0.4s both;
}

.success-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 500;
    animation: successSubtitleSlide 0.6s ease-out 0.6s both;
}

@keyframes successTitleSlide {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes successSubtitleSlide {
    from {
        opacity: 0;
        transform: translateY(15px);
    }
    to {
        opacity: 0.9;
        transform: translateY(0);
    }
}

.success-details {
    padding: 32px;
}

.transaction-info {
    background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 24px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 0.875rem;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-row.total-row {
    font-size: 1.125rem;
    font-weight: 700;
    padding-top: 12px;
    border-top: 2px solid #e2e8f0;
    margin-top: 12px;
}

.info-label {
    color: #64748b;
    font-weight: 500;
}

.info-value {
    color: #0f172a;
    font-weight: 600;
}

.info-value.total-amount {
    color: #10b981;
    font-size: 1.25rem;
    font-weight: 800;
}

.info-value.change-amount {
    color: #059669;
    font-weight: 700;
}

.success-actions {
    padding: 24px 32px;
    background: linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%);
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 12px;
    justify-content: center;
}

.success-actions .btn {
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
}

.success-actions .print-receipt-btn {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    color: #475569;
    border-color: #cbd5e1;
}

.success-actions .print-receipt-btn:hover {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.success-actions .new-sale-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    border: none;
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.success-actions .new-sale-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
}

/* Modal entrance animation */
.modern-success .modal-content {
    animation: modernSuccessEntrance 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes modernSuccessEntrance {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-30px) rotate(-5deg);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0) rotate(0deg);
    }
}

/* Responsive design for success modal */
@media (max-width: 480px) {
    .modern-success-content {
        margin: 16px;
        max-width: none;
        border-radius: 20px;
    }
    
    .success-animation-header {
        padding: 32px 24px 24px;
    }
    
    .success-details {
        padding: 24px;
    }
    
    .success-actions {
        padding: 20px 24px;
        flex-direction: column;
    }
    
    .success-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .success-checkmark {
        width: 64px;
        height: 64px;
        margin-bottom: 16px;
    }
    
    .checkmark-circle {
        width: 64px;
        height: 64px;
    }
    
    .checkmark-circle::before {
        width: 64px;
        height: 64px;
    }
    
    .success-title {
        font-size: 1.5rem;
    }
}

}

.item-count {
    background: var(--light-gray);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

.summary-items {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 1rem;
    scrollbar-width: thin;
    scrollbar-color: var(--medium-gray) transparent;
}

.summary-items::-webkit-scrollbar {
    width: 4px;
}

.summary-items::-webkit-scrollbar-track {
    background: transparent;
}

.summary-items::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 2px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item-info {
    flex: 1;
}

.summary-item-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
}

.summary-item-details {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.summary-item-total {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1rem;
}

.summary-totals {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.25rem;
    border: 1px solid #e2e8f0;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

.total-row:last-child {
    margin-bottom: 0;
}

.total-row.final-total {
    border-top: 2px solid #e2e8f0;
    padding-top: 1rem;
    margin-top: 1rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.checkout-total-amount {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 800;
}

/* Payment Methods Section */
.checkout-payment-section {
    padding: 1.5rem 2.5rem;
    background: white;
}

.payment-section-header {
    margin-bottom: 1.5rem;
}

.payment-section-header h6 {
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.payment-section-header p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.95rem;
}

.instant-payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
}

.payment-method-card {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    overflow: hidden;
}

.payment-method-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left 0.5s;
}

.payment-method-card:hover::before {
    left: 100%;
}

.payment-method-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
    transform: translateY(-3px);
}

.payment-method-card.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(139, 92, 246, 0.05));
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.2);
}

.payment-method-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.payment-method-icon.cash-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.payment-method-icon.card-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.payment-method-icon.mobile-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.payment-method-icon.bank-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.payment-method-details {
    flex: 1;
    position: relative;
    z-index: 1;
}

.payment-method-details h6 {
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem;
    font-size: 1.1rem;
}

.payment-method-details p {
    color: var(--text-secondary);
    margin: 0 0 0.75rem;
    font-size: 0.9rem;
}

/* Enhanced Payment Features */
.payment-features {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.feature-badge {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
    color: var(--primary-color);
    padding: 0.25rem 0.6rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid rgba(99, 102, 241, 0.2);
    display: flex;
    align-items: center;
    gap: 0.3rem;
    transition: all 0.2s ease;
}

.feature-badge i {
    font-size: 0.7rem;
}

.payment-method-card:hover .feature-badge {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(139, 92, 246, 0.15));
    border-color: rgba(99, 102, 241, 0.3);
    transform: scale(1.05);
}

/* Enhanced Payment Cards */
.payment-method-card.enhanced-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e2e8f0;
    border-radius: 20px;
    padding: 1.75rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 1.25rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.payment-method-card.enhanced-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.08), transparent);
    transition: left 0.6s;
}

.payment-method-card.enhanced-card:hover::before {
    left: 100%;
}

.payment-method-card.enhanced-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 12px 40px rgba(99, 102, 241, 0.15);
    transform: translateY(-4px) scale(1.02);
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
}

.payment-method-card.enhanced-card:active {
    transform: translateY(-2px) scale(0.98);
}

/* Enhanced Payment Buttons */
.instant-pay-btn.enhanced-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
    color: white;
    padding: 0.85rem 1.75rem;
    border-radius: 14px;
    font-weight: 700;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.6rem;
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.25);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.instant-pay-btn.enhanced-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.instant-pay-btn.enhanced-btn:hover::before {
    left: 100%;
}

.instant-pay-btn.enhanced-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 30px rgba(99, 102, 241, 0.35);
    background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%);
}

.instant-pay-btn.enhanced-btn:active {
    transform: translateY(-1px) scale(0.98);
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
}

.instant-pay-btn.enhanced-btn i {
    font-size: 1.1rem;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
}

.payment-method-action {
    position: relative;
    z-index: 1;
}

.instant-pay-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.instant-pay-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.instant-pay-btn:active {
    transform: translateY(0) scale(0.98);
}

.instant-pay-btn.processing {
    background: #6b7280;
    cursor: not-allowed;
    transform: none;
}

.instant-pay-btn.processing::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Cash Details Section */
.cash-details-section {
    padding: 1.5rem 2.5rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cash-details-header h6 {
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cash-input-group {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 2rem;
    align-items: end;
    margin-bottom: 1.5rem;
}

.amount-input label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: block;
}

.amount-input .input-group {
    max-width: 200px;
}

.amount-input .form-control {
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
    border-radius: 8px;
}

.change-calculation {
    text-align: right;
}

.change-display {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.change-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.change-amount {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary-color);
}

.cash-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Processing Overlay */
.checkout-processing-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.processing-content {
    text-align: center;
    max-width: 400px;
    padding: 2rem;
}

.processing-spinner {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.processing-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.processing-message {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1rem;
}

.processing-progress {
    text-align: left;
}

.processing-progress .progress {
    height: 8px;
    border-radius: 4px;
    margin-bottom: 0.75rem;
    background-color: #e2e8f0;
}

.processing-progress .progress-bar {
    border-radius: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    transition: width 0.6s ease;
}

.processing-step {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Alternative Actions */
.checkout-alternative-actions {
    padding: 1rem 2.5rem 1.5rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    text-align: center;
}

.btn-full-checkout {
    font-weight: 600;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-full-checkout:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Success Modal Styles */
.payment-success-modal .modal-content {
    border: none;
    border-radius: 20px;
    overflow: hidden;
    text-align: center;
}

.success-header {
    background: linear-gradient(135deg, var(--success-color), #059669);
    padding: 3rem 2rem 2rem;
    color: white;
}

.success-icon {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    margin: 0 auto 1rem;
    animation: successPulse 2s infinite;
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.success-title {
    font-size: 2rem;
    font-weight: 800;
    margin: 0;
}

.success-message {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0.5rem 0 0;
}

/* Success Modal Details Styles */
.success-details {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    text-align: left;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--text-secondary);
}

.detail-value {
    font-weight: 700;
    color: var(--text-primary);
}

/* Enhanced Visual Feedback Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0%, 20%, 40%, 60%, 80% {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }
    0% {
        opacity: 0;
        transform: scale3d(.3, .3, .3);
    }
    20% {
        transform: scale3d(1.1, 1.1, 1.1);
    }
    40% {
        transform: scale3d(.9, .9, .9);
    }
    60% {
        opacity: 1;
        transform: scale3d(1.03, 1.03, 1.03);
    }
    80% {
        transform: scale3d(.97, .97, .97);
    }
    100% {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }
}

.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 400px;
}

.notification {
    background: white;
    border: 1px solid #e0e0e0;
    border-left: 4px solid var(--info-color);
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-success {
    border-left-color: var(--success-color);
}

.notification-warning {
    border-left-color: var(--warning-color);
}

.notification-error {
    border-left-color: var(--danger-color);
}

.notification i {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.notification-success i {
    color: var(--success-color);
}

.notification-warning i {
    color: var(--warning-color);
}

.notification-error i {
    color: var(--danger-color);
}

.notification-info i {
    color: var(--info-color);
}

.notification span {
    flex: 1;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.notification-close {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    color: var(--text-muted);
    border-radius: 4px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.notification-close:hover {
    background: var(--light-gray);
    color: var(--text-primary);
}

/* Enhanced hover effects for better interactivity */
.product-card {
    cursor: pointer;
    user-select: none;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1), 0 0 0 1px var(--primary-color);
    transform: translateY(-3px);
}

/* Out of stock styling */
.product-card[data-product-stock="0"] {
    opacity: 0.6;
    cursor: not-allowed;
}

.product-card[data-product-stock="0"]:hover {
    transform: none;
    box-shadow: var(--shadow-sm);
    border-color: var(--medium-gray);
}

.product-card[data-product-stock="0"]::after {
    content: 'OUT OF STOCK';
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--danger-color);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    text-transform: uppercase;
}

/* Compact Customer Section */
.customer-section {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.6rem 0.75rem;
    margin-top: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.6rem;
    position: relative;
    overflow: hidden;
    min-height: 56px;
}

.customer-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left 0.5s ease;
}

.customer-section:hover {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 3px 12px rgba(99, 102, 241, 0.1);
}

.customer-section:hover::before {
    left: 100%;
}

.customer-avatar {
    width: 36px;
    height: 36px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.2);
    position: relative;
    z-index: 1;
}

.customer-details {
    flex: 1;
    min-width: 0;
    position: relative;
    z-index: 1;
}

.customer-label {
    font-size: 0.7rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 0.1rem;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    line-height: 1;
}

.customer-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.1rem;
    line-height: 1.1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.customer-type {
    font-size: 0.7rem;
    color: var(--primary-color);
    font-weight: 500;
    line-height: 1;
}

.customer-actions {
    color: var(--text-secondary);
    font-size: 1rem;
    transition: all 0.2s ease;
    position: relative;
    z-index: 1;
}

.customer-section:hover .customer-actions {
    color: var(--primary-color);
    transform: translateX(2px);
}

/* Customer Selection Modal */
.customer-search-section {
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 1rem;
}

.customer-quick-actions {
    display: flex;
    gap: 0.75rem;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 1rem;
}

.customer-quick-actions .btn {
    flex: 1;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.customer-list {
    max-height: 400px;
    overflow-y: auto;
    border-radius: 8px;
    background: #f8fafc;
    padding: 0.5rem;
}

.customer-item {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
}

.customer-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.1);
}

.customer-item.selected {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.15);
}

.customer-item .customer-avatar {
    width: 52px;
    height: 52px;
    background: var(--light-gray);
    border: 2px solid #e2e8f0;
    color: var(--text-secondary);
    font-size: 1.3rem;
    box-shadow: none;
}

.customer-item:hover .customer-avatar,
.customer-item.selected .customer-avatar {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

.customer-info {
    flex: 1;
    min-width: 0;
}

.customer-info .customer-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.customer-type-badge {
    font-size: 0.7rem;
    font-weight: 600;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.customer-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    align-items: start;
}

.customer-contact {
    font-size: 0.85rem;
    color: var(--text-secondary);
    line-height: 1.6;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.contact-text {
    flex: 1;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}

.contact-text:hover {
    white-space: normal;
    overflow: visible;
    text-overflow: initial;
    word-break: break-all;
    background: rgba(99, 102, 241, 0.05);
    border-radius: 3px;
    padding: 2px 4px;
    position: relative;
    z-index: 10;
}

.customer-stats {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.8rem;
    align-items: flex-end;
    text-align: right;
}

.customer-number {
    font-weight: 600;
    color: var(--text-primary);
    background: #f1f5f9;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.75rem;
}

.customer-total {
    font-weight: 600;
    color: var(--success-color);
}

.customer-last-visit {
    color: var(--text-secondary);
}

.customer-item .customer-actions {
    color: var(--text-secondary);
    font-size: 1.5rem;
    opacity: 0.5;
    transition: all 0.2s ease;
}

.customer-item:hover .customer-actions,
.customer-item.selected .customer-actions {
    color: var(--success-color);
    opacity: 1;
}

.customer-select-icon {
    transition: all 0.2s ease;
}

.customer-item.selected .customer-select-icon {
    color: var(--success-color);
    transform: scale(1.2);
}

/* Custom scrollbar for customer list */
.customer-list::-webkit-scrollbar {
    width: 6px;
}

.customer-list::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.customer-list::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 3px;
}

.customer-list::-webkit-scrollbar-thumb:hover {
    background: var(--dark-gray);
}

/* Responsive adjustments for customer modal */
@media (max-width: 768px) {
    .customer-details {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .customer-stats {
        align-items: flex-start;
        text-align: left;
    }
    
    .customer-quick-actions {
        flex-direction: column;
    }
}

/* Ripple effect for clicks */
.product-card {
    position: relative;
    overflow: hidden;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(99, 102, 241, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.4s ease, height 0.4s ease;
    pointer-events: none;
    z-index: 1;
}

.product-card:active::before {
    width: 300px;
    height: 300px;
}
